<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Sidebar Background</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Simular as variáveis CSS do projeto */
        :root {
            --bg-primary: #ffffff;
            --border-primary: #e2e8f0;
        }
        
        .dark {
            --bg-primary: #000000;
            --border-primary: #262626;
        }
        
        /* Simular a regra CSS do projeto */
        .sidebar-container {
            border-color: var(--border-primary);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="p-8">
        <h1 class="text-2xl font-bold mb-6">Debug Sidebar Background</h1>
        
        <div class="space-y-4">
            <!-- Teste 1: Sidebar sem classe sidebar-container -->
            <div class="bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800 w-64 h-32 p-4">
                <h3 class="font-semibold">Teste 1: Sem .sidebar-container</h3>
                <p class="text-sm text-gray-600">bg-white dark:bg-black</p>
            </div>
            
            <!-- Teste 2: Sidebar com classe sidebar-container -->
            <div class="sidebar-container bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800 w-64 h-32 p-4">
                <h3 class="font-semibold">Teste 2: Com .sidebar-container</h3>
                <p class="text-sm text-gray-600">sidebar-container + bg-white dark:bg-black</p>
            </div>
            
            <!-- Teste 3: Apenas Tailwind -->
            <div class="bg-white border-r border-gray-200 w-64 h-32 p-4">
                <h3 class="font-semibold">Teste 3: Apenas bg-white</h3>
                <p class="text-sm text-gray-600">Sem dark mode</p>
            </div>
        </div>
        
        <div class="mt-8">
            <button onclick="toggleDarkMode()" class="bg-blue-500 text-white px-4 py-2 rounded">
                Toggle Dark Mode
            </button>
            
            <div class="mt-4 p-4 bg-yellow-100 rounded">
                <h3 class="font-semibold">Instruções:</h3>
                <ol class="list-decimal list-inside text-sm mt-2">
                    <li>Observe os 3 testes acima</li>
                    <li>Clique em "Toggle Dark Mode"</li>
                    <li>Verifique se o Teste 1 e 2 ficam pretos no dark mode</li>
                    <li>Se o Teste 2 não ficar preto, o problema é a classe .sidebar-container</li>
                </ol>
            </div>
        </div>
    </div>
    
    <script>
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }
        
        // Log das classes aplicadas
        console.log('Classes do Teste 1:', document.querySelector('div:nth-of-type(2) > div:nth-of-type(1)').className);
        console.log('Classes do Teste 2:', document.querySelector('div:nth-of-type(2) > div:nth-of-type(2)').className);
        console.log('Classes do Teste 3:', document.querySelector('div:nth-of-type(2) > div:nth-of-type(3)').className);
    </script>
</body>
</html>
