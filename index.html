<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/pinpal-logo-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Firebase Auth popup configuration -->
    <meta http-equiv="Permissions-Policy" content="popup=*, window-management=*" />
    <meta http-equiv="Cross-Origin-Opener-Policy" content="unsafe-none" />
    <meta http-equiv="Cross-Origin-Embedder-Policy" content="unsafe-none" />

    <!-- Preconnect to critical third-party domains -->
    <link rel="preconnect" href="https://firebasestorage.googleapis.com">
    <link rel="preconnect" href="https://securetoken.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Preload critical fonts -->
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
        as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200">
    </noscript>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="PinPal">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/pinpics/pin-example-1.jpg">
    <link rel="apple-touch-icon" sizes="152x152" href="/pinpics/pin-example-1.jpg">
    <link rel="apple-touch-icon" sizes="180x180" href="/pinpics/pin-example-2.jpg">

    <!-- Preload critical images (will be dynamically updated by React) -->
    <!-- <link rel="preload" as="image" href="/pinpal-logo-icon.png"> -->

    <title>PinPal - Disney Pin Trading Platform</title>

    <!-- Theme initialization script -->
    <script>
        // Initialize theme from localStorage or system preference
        try {
            const savedTheme = localStorage.getItem('pinpal-theme');
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            let actualTheme = 'light'; // default

            if (savedTheme === 'dark') {
                actualTheme = 'dark';
            } else if (savedTheme === 'light') {
                actualTheme = 'light';
            } else if (savedTheme === 'system' || !savedTheme) {
                // For system theme or no saved theme, check system preference
                actualTheme = systemPrefersDark ? 'dark' : 'light';
            }

            // Apply initial theme to prevent flash
            if (actualTheme === 'dark') {
                document.documentElement.classList.add('dark');
                document.documentElement.style.colorScheme = 'dark';
            } else {
                document.documentElement.classList.remove('dark');
                document.documentElement.style.colorScheme = 'light';
            }

            console.log(`🎨 Initial theme applied: ${actualTheme} (saved: ${savedTheme}, system: ${systemPrefersDark ? 'dark' : 'light'})`);
        } catch (e) {
            console.log('⚠️ Error accessing localStorage for theme:', e);
        }
    </script>
</head>

<body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Script de teste para debug do foco do campo de endereço -->
    <script>
        // Aguardar o carregamento da página
        window.addEventListener('load', () => {
            // Só carregar se estivermos na página de trading points
            if (window.location.pathname.includes('/admin/trading-points')) {
                setTimeout(() => {
                    // Script para testar o comportamento do foco no campo de endereço
                    function testAddressFocus() {
                        console.log('🧪 Testando comportamento do foco no campo de endereço...');

                        const addressInput = document.querySelector('input[placeholder*="Search for a business"]') ||
                            document.querySelector('input[placeholder*="address"]');

                        if (!addressInput) {
                            console.error('❌ Campo de endereço não encontrado');
                            return;
                        }

                        console.log('✅ Campo de endereço encontrado:', addressInput);
                        addressInput.focus();
                        console.log('🎯 Foco aplicado ao campo');

                        const testText = 'restaurant';
                        let currentText = '';

                        console.log('⌨️ Iniciando simulação de digitação...');

                        for (let i = 0; i < testText.length; i++) {
                            setTimeout(() => {
                                currentText += testText[i];
                                addressInput.value = currentText;
                                addressInput.dispatchEvent(new Event('input', { bubbles: true }));

                                console.log(`📝 Digitado: "${currentText}"`);
                                console.log(`🎯 Elemento focado: ${document.activeElement === addressInput ? 'SIM' : 'NÃO'}`);

                                if (document.activeElement !== addressInput) {
                                    console.error(`❌ FOCO PERDIDO após digitar "${currentText}"`);
                                    console.log(`🔍 Elemento atual com foco:`, document.activeElement);
                                } else {
                                    console.log(`✅ Foco mantido após digitar "${currentText}"`);
                                }

                                // Verificar se sugestões apareceram
                                const suggestions = document.querySelector('.absolute.z-50');
                                if (suggestions) {
                                    console.log(`💡 Sugestões visíveis: ${suggestions.children.length} itens`);

                                    // Verificar se o foco ainda está no input após sugestões aparecerem
                                    setTimeout(() => {
                                        if (document.activeElement !== addressInput) {
                                            console.error(`❌ FOCO PERDIDO após sugestões carregarem para "${currentText}"`);
                                            console.log(`🔍 Elemento com foco após sugestões:`, document.activeElement);
                                        } else {
                                            console.log(`✅ Foco mantido após sugestões carregarem para "${currentText}"`);
                                        }
                                    }, 500); // Aguardar 500ms para sugestões carregarem
                                }

                            }, i * 800); // Aumentado para 800ms para dar tempo das sugestões carregarem
                        }

                        // Teste final após 8 segundos
                        setTimeout(() => {
                            console.log('🏁 Teste finalizado');
                            console.log(`🎯 Foco final: ${document.activeElement === addressInput ? 'MANTIDO' : 'PERDIDO'}`);

                            const suggestions = document.querySelector('.absolute.z-50');
                            if (suggestions) {
                                console.log(`💡 Sugestões finais: ${suggestions.children.length} itens`);
                            } else {
                                console.log('💡 Nenhuma sugestão visível');
                            }
                        }, testText.length * 800 + 2000);
                    }

                    function testContinuousTyping() {
                        console.log('🧪 Testando digitação contínua...');

                        const addressInput = document.querySelector('input[placeholder*="Search for a business"]') ||
                            document.querySelector('input[placeholder*="address"]');

                        if (!addressInput) {
                            console.error('❌ Campo de endereço não encontrado');
                            return;
                        }

                        addressInput.focus();
                        console.log('🎯 Foco inicial aplicado');

                        const testPhrase = 'restaurant in orlando';
                        let currentText = '';

                        // Simular digitação mais rápida e contínua
                        for (let i = 0; i < testPhrase.length; i++) {
                            setTimeout(() => {
                                currentText += testPhrase[i];
                                addressInput.value = currentText;
                                addressInput.dispatchEvent(new Event('input', { bubbles: true }));

                                // Verificar foco imediatamente
                                const hasFocus = document.activeElement === addressInput;
                                console.log(`📝 "${currentText}" - Foco: ${hasFocus ? '✅' : '❌'}`);

                                if (!hasFocus) {
                                    console.error(`❌ FOCO PERDIDO durante digitação contínua em "${currentText}"`);
                                    // Tentar restaurar foco
                                    addressInput.focus();
                                    console.log('🔧 Tentando restaurar foco...');
                                }

                            }, i * 150); // 150ms entre cada caractere
                        }
                    }

                    function debugAddressFocus() {
                        console.log('🔍 Debug completo do campo de endereço:');

                        const addressInput = document.querySelector('input[placeholder*="Search for a business"]') ||
                            document.querySelector('input[placeholder*="address"]');

                        if (!addressInput) {
                            console.error('❌ Campo de endereço não encontrado');
                            return;
                        }

                        console.log('📋 Informações do campo:');
                        console.log('- Elemento:', addressInput);
                        console.log('- Valor atual:', addressInput.value);
                        console.log('- Está focado:', document.activeElement === addressInput);
                        console.log('- Está desabilitado:', addressInput.disabled);
                        console.log('- Classes CSS:', addressInput.className);
                        console.log('- AutoComplete:', addressInput.getAttribute('autocomplete'));
                        console.log('- SpellCheck:', addressInput.getAttribute('spellcheck'));

                        const container = addressInput.closest('.relative');
                        console.log('- Container:', container);

                        const suggestions = document.querySelector('.absolute.z-50');
                        console.log('- Sugestões visíveis:', !!suggestions);
                        if (suggestions) {
                            console.log('- Número de sugestões:', suggestions.children.length);
                        }

                        // Verificar event listeners
                        console.log('- Event listeners disponíveis:', Object.getOwnPropertyNames(addressInput).filter(prop => prop.startsWith('on')));
                    }

                    // Expor funções globalmente
                    window.testAddressFocus = testAddressFocus;
                    window.testContinuousTyping = testContinuousTyping;
                    window.debugAddressFocus = debugAddressFocus;

                    console.log('🧪 Funções de teste carregadas:');
                    console.log('- testAddressFocus() - Testa digitação com pausa para sugestões');
                    console.log('- testContinuousTyping() - Testa digitação contínua rápida');
                    console.log('- debugAddressFocus() - Debug completo do campo');
                    console.log('- testGooglePlacesAPI() - Testa API do Google Places diretamente');
                }, 2000); // Aguardar 2 segundos para o React carregar
            }
        });

        // Função para testar a API do Google Places diretamente
        window.testGooglePlacesAPI = async function () {
            console.log('🧪 Testando API do Google Places...');

            try {
                const response = await fetch('https://places.googleapis.com/v1/places:autocomplete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Goog-Api-Key': 'AIzaSyCsK8De7eoNEVV93I2Du0szP5wGKl1rTws'
                    },
                    body: JSON.stringify({
                        input: "rio de janeiro",
                        locationBias: {
                            circle: {
                                center: {
                                    latitude: -22.9068,
                                    longitude: -43.1729
                                },
                                radius: 50000.0
                            }
                        },
                        languageCode: "pt-BR"
                    })
                });

                console.log('📡 Response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ API Error:', response.status, errorText);
                    return;
                }

                const data = await response.json();
                console.log('📦 API Response:', JSON.stringify(data, null, 2));

                if (data.suggestions && data.suggestions.length > 0) {
                    console.log('✅ Estrutura da primeira sugestão:');
                    const firstSuggestion = data.suggestions[0];
                    console.log('- placePrediction:', firstSuggestion.placePrediction);

                    if (firstSuggestion.placePrediction) {
                        const prediction = firstSuggestion.placePrediction;
                        console.log('- place:', prediction.place);
                        console.log('- placeId:', prediction.placeId);
                        console.log('- text:', prediction.text);
                        console.log('- structuredFormat:', prediction.structuredFormat);
                    }
                }

            } catch (error) {
                console.error('❌ Erro no teste da API:', error);
            }
        };
    </script>
</body>

</html>