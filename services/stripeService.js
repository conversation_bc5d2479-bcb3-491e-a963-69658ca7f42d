import Stripe from 'stripe';
import { getPool } from '../config/database.js';

class StripeService {
  constructor() {
    // Initialize Stripe with secret key from environment
    console.log('🔑 STRIPE_SECRET_KEY from env:', process.env.STRIPE_SECRET_KEY ? `${process.env.STRIPE_SECRET_KEY.substring(0, 20)}...` : 'NOT SET');
    
    this.stripe = process.env.STRIPE_SECRET_KEY 
      ? new Stripe(process.env.STRIPE_SECRET_KEY, {
          apiVersion: '2023-10-16',
        })
      : null;
    
    this.platformFeePercentage = parseFloat(process.env.STRIPE_PLATFORM_FEE_PERCENTAGE) || 5.0;
    this.currency = process.env.MARKETPLACE_CURRENCY || 'usd';
    this.baseUrl = process.env.MARKETPLACE_BASE_URL || 'http://localhost:5773';
    this.isConfigured = !!process.env.STRIPE_SECRET_KEY;
    
    if (!this.isConfigured) {
      console.warn('⚠️  Stripe not configured - set STRIPE_SECRET_KEY in environment');
    } else {
      console.log('✅ Stripe service initialized successfully');
    }
  }

  // CUSTOMER MANAGEMENT
  async createOrRetrieveCustomer(userData) {
    console.log('🔄 Creating or retrieving customer for user:', userData.userId);
    
    if (!this.isConfigured) {
      console.log('🧪 Using mock customer');
      return this._mockCustomer(userData);
    }

    try {
      const { userId, email, firstName, lastName, displayName } = userData;
      
      // First, check if customer already exists in our database
      const existingCustomer = await this._getCustomerFromDatabase(userId);
      if (existingCustomer && existingCustomer.stripe_customer_id) {
        console.log('✅ Found existing customer:', existingCustomer.stripe_customer_id);
        
        // Verify customer still exists in Stripe
        try {
          const stripeCustomer = await this.stripe.customers.retrieve(existingCustomer.stripe_customer_id);
          if (!stripeCustomer.deleted) {
            return {
              customerId: stripeCustomer.id,
              email: stripeCustomer.email,
              name: stripeCustomer.name,
              success: true
            };
          }
        } catch (error) {
          console.warn('⚠️ Customer not found in Stripe, creating new one');
        }
      }

      // Create new customer in Stripe
      const customerData = {
        email: email,
        name: displayName || `${firstName || ''} ${lastName || ''}`.trim() || undefined,
        metadata: {
          userId: userId,
          platform: 'pinpal'
        }
      };

      console.log('🆕 Creating new Stripe customer:', customerData);
      const stripeCustomer = await this.stripe.customers.create(customerData);
      
      // Save customer ID to database
      await this._saveCustomerToDatabase(userId, stripeCustomer.id);
      
      console.log('✅ Customer created successfully:', stripeCustomer.id);
      return {
        customerId: stripeCustomer.id,
        email: stripeCustomer.email,
        name: stripeCustomer.name,
        success: true
      };

    } catch (error) {
      console.error('Error creating/retrieving customer:', error);
      throw new Error(`Failed to create/retrieve customer: ${error.message}`);
    }
  }

  async updateCustomer(customerId, updateData) {
    if (!this.isConfigured) {
      return this._mockCustomer({ userId: 'mock', ...updateData });
    }

    try {
      const stripeCustomer = await this.stripe.customers.update(customerId, updateData);
      return {
        customerId: stripeCustomer.id,
        email: stripeCustomer.email,
        name: stripeCustomer.name,
        success: true
      };
    } catch (error) {
      console.error('Error updating customer:', error);
      throw new Error(`Failed to update customer: ${error.message}`);
    }
  }

  // PAYMENT INTENTS WITH CUSTOMER AND CONNECT
  async createPaymentIntent(transactionData) {
    console.log('🔄 Creating payment intent with data:', transactionData);
    console.log('🔧 Stripe configured:', this.isConfigured);
    
    if (!this.isConfigured) {
      console.log('🧪 Using mock payment intent');
      return this._mockPaymentIntent(transactionData);
    }

    try {
      const { amount, pinId, userId, sellerId, description, customerData } = transactionData;
      
      // Convert amount to cents for Stripe (fix the decimal issue)
      console.log('💰 Amount received (dollars):', amount);
      const amountInCents = Math.round(parseFloat(amount) * 100);
      console.log('💰 Amount converted (cents):', amountInCents);
      const platformFee = this.calculatePlatformFee(amountInCents);
      const sellerAmount = amountInCents - platformFee;
      console.log('💰 Platform fee (cents):', platformFee);
      console.log('💰 Seller amount (cents):', sellerAmount);

      // Get seller's Stripe Connect account
      const sellerStripeAccount = await this._getSellerStripeAccount(sellerId);
      console.log('👤 Seller Stripe account:', sellerStripeAccount?.stripe_account_id || 'NOT_CONNECTED');

      // Create or retrieve customer
      let stripeCustomer = null;
      if (customerData) {
        const customerResult = await this.createOrRetrieveCustomer(customerData);
        if (customerResult.success) {
          stripeCustomer = { id: customerResult.customerId };
          console.log('👤 Using customer:', stripeCustomer.id);
        }
      }

      // Create payment intent with application fee (if seller has Connect account)
      const paymentIntentData = {
        amount: amountInCents,
        currency: this.currency,
        metadata: {
          pinId,
          userId,
          sellerId,
          type: 'pin_purchase',
          platformFee: platformFee.toString(),
          sellerAmount: sellerAmount.toString()
        },
        description: description || `Purchase of pin ${pinId}`,
        automatic_payment_methods: {
          enabled: true,
        },
      };

      // Add customer if available
      if (stripeCustomer) {
        paymentIntentData.customer = stripeCustomer.id;
      }

      // 🎯 PINPAL SEMPRE COBRA 5% - ESTRATÉGIA BASEADA NO STATUS DO VENDEDOR
      console.log('💰 PinPal platform fee calculation:', platformFee, 'cents');
      
      // 🧪 TEST MODE: Force application fee without transfer for dashboard testing
      const testModeForceApplicationFee = process.env.STRIPE_TEST_MODE_FORCE_APPLICATION_FEE === 'true';
      
      if (testModeForceApplicationFee) {
        console.log('🧪 TEST MODE: Creating payment intent with application fee but no transfer');
        paymentIntentData.application_fee_amount = platformFee;
        // No transfer_data - PinPal keeps everything, will transfer manually later
        console.log('💸 Application fee (PinPal):', platformFee, 'cents');
        console.log('💸 Seller amount (manual transfer later):', sellerAmount, 'cents');
      } else if (sellerStripeAccount?.stripe_account_id && sellerStripeAccount?.stripe_onboarding_completed) {
        console.log('✅ Seller connected - Using application_fee + transfer_data');
        
        // VENDEDOR CONECTADO: Application Fee + Transfer automático
        paymentIntentData.application_fee_amount = platformFee;
        paymentIntentData.transfer_data = {
          destination: sellerStripeAccount.stripe_account_id,
        };
        
        console.log('🔗 Connect account:', sellerStripeAccount.stripe_account_id);
        console.log('💸 Application fee (PinPal):', platformFee, 'cents');
        console.log('💸 Transfer to seller:', sellerAmount, 'cents');
      } else {
        console.log('⚠️ Seller not connected - PinPal collects full amount, will transfer later');
        console.log('📝 Strategy: Collect $' + amount + ', hold seller portion until Connect setup');
        
        // VENDEDOR NÃO CONECTADO: PinPal recebe tudo, faz transfer manual depois
        // Não usar application_fee_amount pois não temos destino
        // PinPal vai segurar os fundos e transferir quando vendedor se conectar
        paymentIntentData.metadata.requires_manual_transfer = 'true';
        paymentIntentData.metadata.seller_not_connected = 'true';
        paymentIntentData.metadata.platform_fee_amount = platformFee.toString();
        paymentIntentData.metadata.seller_amount_pending = sellerAmount.toString();
        paymentIntentData.metadata.transfer_strategy = 'manual_after_connect';
        
        console.log('💰 Full amount to PinPal:', amountInCents, 'cents');
        console.log('💰 Platform fee (reserved):', platformFee, 'cents');
        console.log('💰 Seller amount (pending):', sellerAmount, 'cents');
      }

      console.log('🔄 Creating Stripe payment intent:', {
        amount: paymentIntentData.amount,
        application_fee_amount: paymentIntentData.application_fee_amount,
        transfer_data: paymentIntentData.transfer_data,
        customer: paymentIntentData.customer
      });

      const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentData);

      return {
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret,
        amount: parseFloat(amount), // Keep as decimal for frontend
        platformFee: platformFee / 100, // Convert back to dollars
        sellerAmount: sellerAmount / 100,
        customerId: stripeCustomer?.id,
        sellerConnected: !!sellerStripeAccount?.stripe_onboarding_completed,
        transferMethod: sellerStripeAccount?.stripe_onboarding_completed ? 'automatic' : 'manual',
        success: true,
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw new Error(`Failed to create payment intent: ${error.message}`);
    }
  }

  async confirmPaymentIntent(paymentIntentId) {
    if (!this.isConfigured) {
      return this._mockConfirmPayment(paymentIntentId);
    }

    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      
      return {
        success: true,
        status: paymentIntent.status,
        chargeId: paymentIntent.charges?.data[0]?.id,
        transferId: paymentIntent.transfer,
        amount: paymentIntent.amount / 100,
      };
    } catch (error) {
      console.error('Error confirming payment intent:', error);
      throw new Error(`Failed to confirm payment intent: ${error.message}`);
    }
  }

  // STRIPE CONNECT ACCOUNT MANAGEMENT
  async createConnectAccount(userData) {
    console.log('🔄 Creating Stripe Connect account for:', userData.userId);
    
    if (!this.isConfigured) {
      console.log('🧪 Using mock Connect account');
      return {
        accountId: `acct_mock_${userData.userId}`,
        onboardingUrl: `https://connect.stripe.com/setup/mock/${userData.userId}`,
        success: true
      };
    }

    try {
      // Criar conta Connect
      const account = await this.stripe.accounts.create({
        type: 'express',
        country: 'US', // TODO: Detectar país do usuário
        email: userData.email,
        metadata: {
          userId: userData.userId,
          platform: 'pinpal'
        }
      });

      console.log('✅ Stripe Connect account created:', account.id);

      // Criar link de onboarding
      const accountLink = await this.stripe.accountLinks.create({
        account: account.id,
        refresh_url: `${this.baseUrl}/seller/onboarding/refresh`,
        return_url: `${this.baseUrl}/seller/onboarding/complete`,
        type: 'account_onboarding',
      });

      console.log('✅ Onboarding link created:', accountLink.url);

      return {
        accountId: account.id,
        onboardingUrl: accountLink.url,
        success: true
      };

    } catch (error) {
      console.error('Error creating Stripe Connect account:', error);
      throw new Error(`Failed to create Stripe Connect account: ${error.message}`);
    }
  }

  /**
   * Criar conta Stripe Connect com dados coletados internamente (sem redirect)
   * Esta função cria uma conta completamente configurada usando dados do PinPal
   */
  async createConnectAccountInternal(accountData) {
    console.log('🏪 Creating internal Stripe Connect account with complete data');
    
    if (!this.isConfigured) {
      console.log('🧪 Using mock internal Connect account');
      return {
        accountId: `acct_internal_mock_${accountData.userId}`,
        onboardingComplete: true,
        chargesEnabled: true,
        payoutsEnabled: true,
        success: true
      };
    }

    try {
      const { userId, personalInfo, address, bankAccount, business } = accountData;

      console.log('🔍 DEBUG - Full accountData received:', JSON.stringify(accountData, null, 2));
      console.log('🔍 DEBUG - Business object:', JSON.stringify(business, null, 2));

      // Criar conta Connect com tipo Custom (mais controle)
      const account = await this.stripe.accounts.create({
        type: 'custom',
        country: address.country || 'US',
        email: personalInfo.email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true }
        },
        business_type: business.type || 'individual',
        metadata: {
          userId: userId,
          platform: 'pinpal',
          onboarding_method: 'internal'
        }
      });

      console.log('✅ Custom Stripe Connect account created:', account.id);

      // Configurar informações pessoais
      const dobParts = personalInfo.dateOfBirth.split('-');
      const individual = {
        first_name: personalInfo.firstName,
        last_name: personalInfo.lastName,
        email: personalInfo.email,
        phone: personalInfo.phone,
        dob: {
          day: parseInt(dobParts[2]),
          month: parseInt(dobParts[1]),
          year: parseInt(dobParts[0])
        },
        address: {
          line1: address.line1,
          line2: address.line2 || undefined,
          city: address.city,
          state: address.state,
          postal_code: address.postalCode,
          country: address.country || 'US'
        }
      };

      // Atualizar conta com informações pessoais
      await this.stripe.accounts.update(account.id, {
        individual: individual,
        business_profile: {
          mcc: '5944', // Jewelry stores
          product_description: 'Pin collecting and trading marketplace'
        },
        tos_acceptance: {
          date: Math.floor(Date.now() / 1000),
          ip: '127.0.0.1' // TODO: Capturar IP real do usuário
        }
      });

      console.log('✅ Account updated with personal information');

      // Criar conta bancária externa
      const externalAccount = await this.stripe.accounts.createExternalAccount(
        account.id,
        {
          external_account: {
            object: 'bank_account',
            country: address.country || 'US',
            currency: 'usd',
            routing_number: bankAccount.routingNumber,
            account_number: bankAccount.accountNumber,
            account_holder_name: bankAccount.accountHolderName,
            account_holder_type: business.type === 'company' ? 'company' : 'individual'
          }
        }
      );

      console.log('✅ Bank account added:', externalAccount.id);

      // Verificar status da conta
      const updatedAccount = await this.stripe.accounts.retrieve(account.id);
      
      const isComplete = updatedAccount.charges_enabled && updatedAccount.payouts_enabled;
      
      console.log('📊 Account status:', {
        charges_enabled: updatedAccount.charges_enabled,
        payouts_enabled: updatedAccount.payouts_enabled,
        details_submitted: updatedAccount.details_submitted,
        requirements: updatedAccount.requirements.currently_due
      });

      return {
        accountId: account.id,
        onboardingComplete: isComplete,
        chargesEnabled: updatedAccount.charges_enabled,
        payoutsEnabled: updatedAccount.payouts_enabled,
        requiresAdditionalInfo: updatedAccount.requirements.currently_due.length > 0,
        pendingRequirements: updatedAccount.requirements.currently_due,
        success: true
      };

    } catch (error) {
      console.error('Error creating internal Stripe Connect account:', error);
      
      // Log detalhado do erro para debug
      if (error.type === 'StripeInvalidRequestError') {
        console.error('Stripe validation error:', {
          code: error.code,
          param: error.param,
          message: error.message
        });
      }
      
      return {
        success: false,
        error: error.message || 'Failed to create Stripe Connect account'
      };
    }
  }

  async getAccountStatus(accountId) {
    console.log('🔍 Getting Stripe account status for:', accountId);
    
    if (!this.isConfigured) {
      return {
        chargesEnabled: true,
        payoutsEnabled: true,
        detailsSubmitted: true,
        capabilities: {
          card_payments: 'active',
          transfers: 'active'
        }
      };
    }

    try {
      const account = await this.stripe.accounts.retrieve(accountId);
      
      return {
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        detailsSubmitted: account.details_submitted,
        capabilities: account.capabilities,
        requirements: account.requirements,
        accountType: account.type
      };
    } catch (error) {
      console.error('Error getting account status:', error);
      throw error;
    }
  }

  async createOnboardingLink(accountId) {
    console.log('🔗 Creating onboarding link for account:', accountId);
    
    if (!this.isConfigured) {
      console.log('🧪 Using mock onboarding link');
      return {
        url: `https://connect.stripe.com/setup/mock/${accountId}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };
    }

    try {
      const accountLink = await this.stripe.accountLinks.create({
        account: accountId,
        refresh_url: `${this.baseUrl}/seller/onboarding/refresh`,
        return_url: `${this.baseUrl}/seller/onboarding/complete`,
        type: 'account_onboarding',
      });

      return {
        url: accountLink.url,
        expiresAt: new Date(accountLink.expires_at * 1000)
      };

    } catch (error) {
      console.error('Error creating onboarding link:', error);
      throw new Error(`Failed to create onboarding link: ${error.message}`);
    }
  }

  // REFUNDS
  async createRefund(chargeId, amount = null, reason = 'requested_by_customer') {
    if (!this.isConfigured) {
      return this._mockRefund(chargeId, amount);
    }

    try {
      const refund = await this.stripe.refunds.create({
        charge: chargeId,
        amount: amount ? Math.round(amount * 100) : undefined, // Convert to cents
        reason: reason,
      });

      return {
        refundId: refund.id,
        amount: refund.amount / 100, // Convert back to dollars
        status: refund.status,
        success: true,
      };
    } catch (error) {
      console.error('Error creating refund:', error);
      throw new Error(`Failed to create refund: ${error.message}`);
    }
  }

  // WEBHOOKS
  validateWebhookSignature(payload, signature) {
    if (!this.isConfigured) {
      console.log('Mock: Validating webhook signature');
      return { type: 'mock.event', data: {} };
    }

    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );
      return event;
    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      throw new Error('Invalid webhook signature');
    }
  }

  // UTILITY METHODS
  calculatePlatformFee(amount) {
    return Math.round(amount * (this.platformFeePercentage / 100));
  }

  formatPrice(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  // DATABASE HELPERS
  async _saveStripeAccountId(userId, accountId) {
    try {
      const pool = await getPool();
      await pool.query(
        'UPDATE "user" SET stripe_account_id = $1 WHERE id = $2',
        [accountId, userId]
      );
    } catch (error) {
      console.error('Error saving Stripe account ID:', error);
    }
  }

  async updateUserStripeStatus(userId, accountStatus) {
    try {
      const pool = await getPool();
      await pool.query(`
        UPDATE "user" SET 
          stripe_charges_enabled = $1,
          stripe_payouts_enabled = $2,
          stripe_details_submitted = $3,
          stripe_onboarding_completed = $4
        WHERE id = $5
      `, [
        accountStatus.chargesEnabled,
        accountStatus.payoutsEnabled,
        accountStatus.detailsSubmitted,
        accountStatus.chargesEnabled && accountStatus.payoutsEnabled,
        userId
      ]);
      return { success: true };
    } catch (error) {
      console.error('Error updating user Stripe status:', error);
      throw error;
    }
  }

  // DATABASE HELPERS FOR CUSTOMERS
  async _getCustomerFromDatabase(userId) {
    try {
      const pool = await getPool();
      const result = await pool.query(
        'SELECT stripe_customer_id FROM "user" WHERE id = $1',
        [userId]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting customer from database:', error);
      return null;
    }
  }

  async _saveCustomerToDatabase(userId, customerId) {
    try {
      const pool = await getPool();
      await pool.query(
        'UPDATE "user" SET stripe_customer_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [customerId, userId]
      );
    } catch (error) {
      console.error('Error saving customer to database:', error);
    }
  }

  // DATABASE HELPERS FOR STRIPE CONNECT
  async _getSellerStripeAccount(sellerId) {
    try {
      const pool = await getPool();
      const result = await pool.query(`
        SELECT 
          stripe_account_id,
          stripe_charges_enabled,
          stripe_payouts_enabled,
          stripe_details_submitted,
          stripe_onboarding_completed,
          email,
          first_name,
          last_name
        FROM "user" 
        WHERE id = $1
      `, [sellerId]);
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting seller Stripe account:', error);
      return null;
    }
  }

  // MOCK METHODS (fallback when Stripe is not configured)
  _mockPaymentIntent(transactionData) {
    const { amount } = transactionData;
    const platformFee = this.calculatePlatformFee(amount * 100) / 100;
    
    return {
      paymentIntentId: `pi_mock_${Date.now()}`,
      clientSecret: `pi_mock_${Date.now()}_secret`,
      amount,
      platformFee,
      sellerAmount: amount - platformFee,
      success: true,
    };
  }

  _mockConfirmPayment(paymentIntentId) {
    return {
      success: true,
      status: 'succeeded',
      chargeId: `ch_mock_${Date.now()}`,
      transferId: `tr_mock_${Date.now()}`,
    };
  }

  _mockRefund(chargeId, amount) {
    return {
      refundId: `re_mock_${Date.now()}`,
      amount: amount || 100,
      status: 'succeeded',
      success: true,
    };
  }

  _mockCustomer(userData) {
    return {
      customerId: `cus_mock_${userData.userId}`,
      email: userData.email,
      name: userData.displayName || `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),
      success: true
    };
  }

  // MARKETPLACE SETTINGS
  async getMarketplaceSettings() {
    return {
      platformFeePercentage: this.platformFeePercentage,
      currency: this.currency,
      minSalePrice: parseFloat(process.env.MARKETPLACE_MIN_SALE_PRICE) / 100 || 1.00,
      maxSalePrice: parseFloat(process.env.MARKETPLACE_MAX_SALE_PRICE) / 100 || 999.99,
      autoTransferEnabled: process.env.AUTO_TRANSFER_ENABLED === 'true',
      sellerVerificationRequired: process.env.SELLER_VERIFICATION_REQUIRED === 'true',
    };
  }

  async updateMarketplaceSettings(settings) {
    console.log('Updating marketplace settings', settings);
    return { success: true };
  }

  async getSellerDashboard(userId) {
    // This would typically fetch real data from Stripe and database
    return {
      accountId: `acct_${userId}`,
      balance: { available: 0, pending: 0 },
      totalEarnings: 0,
      totalSales: 0,
      recentTransactions: [],
      payoutSchedule: 'daily',
      nextPayoutDate: new Date(Date.now() + ********).toISOString(),
    };
  }

  // =========================================================================
  // TRANSFERS / PAYOUTS
  // =========================================================================
  async createTransferToSeller(sellerId, amountCents, metadata = {}) {
    if (!this.isConfigured) {
      console.log('🧪 Mock transfer to seller', sellerId, amountCents);
      return {
        id: `tr_mock_${Date.now()}`,
        amount: amountCents,
        currency: this.currency,
        destination: 'acct_mock',
        status: 'paid',
        mock: true
      };
    }

    try {
      const sellerAccount = await this._getSellerStripeAccount(sellerId);
      if (!sellerAccount || !sellerAccount.stripe_account_id) {
        throw new Error('Seller does not have a Stripe account');
      }

      const transfer = await this.stripe.transfers.create({
        amount: amountCents,
        currency: this.currency,
        destination: sellerAccount.stripe_account_id,
        metadata: {
          sellerId,
          ...metadata
        }
      });

      return transfer;
    } catch (error) {
      console.error('Error creating transfer:', error);
      throw error;
    }
  }

  /**
   * Create withdrawal transfer - simple transfer of already-earned funds
   * Platform fees were already collected during the original sales
   */
  async createWithdrawalTransfer(sellerId, requestedAmountCents, metadata = {}) {
    if (!this.isConfigured) {
      console.log('🧪 Mock withdrawal transfer', sellerId, requestedAmountCents);
      return {
        id: `tr_mock_${Date.now()}`,
        amount: requestedAmountCents,
        currency: this.currency,
        destination: 'acct_mock',
        status: 'paid',
        mock: true,
        description: `Withdrawal of $${(requestedAmountCents / 100).toFixed(2)}`
      };
    }

    try {
      const sellerAccount = await this._getSellerStripeAccount(sellerId);
      if (!sellerAccount || !sellerAccount.stripe_account_id) {
        throw new Error('Seller does not have a Stripe account');
      }

      console.log('💰 Creating withdrawal transfer:');
      console.log('  - Amount:', requestedAmountCents, 'cents ($' + (requestedAmountCents / 100).toFixed(2) + ')');
      console.log('  - To account:', sellerAccount.stripe_account_id);
      console.log('  - Note: Platform fees were already collected during original sales');

      // Simple transfer - no additional fees since they were collected during sales
      const transfer = await this.stripe.transfers.create({
        amount: requestedAmountCents,
        currency: this.currency,
        destination: sellerAccount.stripe_account_id,
        description: `PinPal withdrawal - $${(requestedAmountCents / 100).toFixed(2)}`,
        metadata: {
          type: 'withdrawal',
          sellerId,
          withdrawalId: metadata.withdrawalId || 'unknown',
          note: 'Platform fees already collected during sales',
          ...metadata
        }
      });

      console.log('✅ Transfer created successfully:', transfer.id);

      return {
        id: transfer.id,
        amount: transfer.amount,
        currency: transfer.currency,
        destination: transfer.destination,
        status: transfer.status || 'paid',
        description: transfer.description
      };

    } catch (error) {
      console.error('Error creating withdrawal transfer:', error);
      throw error;
    }
  }

  /**
   * Enable required capabilities for an existing Stripe Connect account
   * This method automatically configures the account for marketplace operations
   */
  async enableAccountCapabilities(accountId, requiredCapabilities = ['card_payments', 'transfers']) {
    console.log('🔧 Enabling capabilities for account:', accountId);
    console.log('📋 Required capabilities:', requiredCapabilities);
    
    if (!this.isConfigured) {
      console.log('🧪 Mock: Capabilities enabled successfully');
      return {
        success: true,
        capabilities: requiredCapabilities.reduce((acc, cap) => {
          acc[cap] = 'active';
          return acc;
        }, {}),
        mock: true
      };
    }

    try {
      // First, get current account status
      const currentAccount = await this.stripe.accounts.retrieve(accountId);
      console.log('📊 Current capabilities:', currentAccount.capabilities);
      
      // Prepare capabilities update
      const capabilitiesToUpdate = {};
      let needsUpdate = false;
      
      for (const capability of requiredCapabilities) {
        const currentStatus = currentAccount.capabilities[capability];
        
        if (currentStatus !== 'active') {
          console.log(`🔄 Enabling ${capability} (current: ${currentStatus || 'not_requested'})`);
          capabilitiesToUpdate[capability] = { requested: true };
          needsUpdate = true;
        } else {
          console.log(`✅ ${capability} already active`);
        }
      }
      
      if (!needsUpdate) {
        console.log('✅ All required capabilities already active');
        return {
          success: true,
          capabilities: currentAccount.capabilities,
          message: 'All capabilities already active'
        };
      }
      
      // Update account capabilities
      console.log('🔄 Updating account capabilities...');
      const updatedAccount = await this.stripe.accounts.update(accountId, {
        capabilities: capabilitiesToUpdate
      });
      
      console.log('✅ Capabilities update requested');
      console.log('📊 Updated capabilities:', updatedAccount.capabilities);
      
      // Check if any capabilities are still pending
      const pendingCapabilities = [];
      const activeCapabilities = [];
      
      for (const capability of requiredCapabilities) {
        const status = updatedAccount.capabilities[capability];
        if (status === 'active') {
          activeCapabilities.push(capability);
        } else if (status === 'pending') {
          pendingCapabilities.push(capability);
        }
      }
      
      return {
        success: true,
        capabilities: updatedAccount.capabilities,
        activeCapabilities,
        pendingCapabilities,
        message: pendingCapabilities.length > 0 
          ? `Capabilities requested, ${pendingCapabilities.join(', ')} pending approval`
          : 'All capabilities active'
      };
      
    } catch (error) {
      console.error('Error enabling capabilities:', error);
      
      if (error.type === 'StripeInvalidRequestError') {
        console.error('Stripe validation error:', {
          code: error.code,
          param: error.param,
          message: error.message
        });
        
        // Some accounts might need additional information before capabilities can be enabled
        if (error.code === 'account_invalid') {
          return {
            success: false,
            error: 'Account needs additional information before capabilities can be enabled',
            requiresOnboarding: true,
            message: 'Complete account onboarding first'
          };
        }
      }
      
      throw error;
    }
  }

  /**
   * Complete account setup with test data for development
   * This method automatically fills required information to activate capabilities
   */
  async completeAccountForTesting(accountId) {
    console.log('🧪 Completing account for testing:', accountId);
    
    if (!this.isConfigured) {
      console.log('🧪 Mock: Account completed for testing');
      return {
        success: true,
        chargesEnabled: true,
        payoutsEnabled: true,
        capabilities: {
          card_payments: 'active',
          transfers: 'active'
        },
        mock: true
      };
    }

    try {
      // First check current requirements
      const currentAccount = await this.stripe.accounts.retrieve(accountId);
      const requirements = currentAccount.requirements.currently_due;
      
      console.log('📋 Current requirements:', requirements);
      
      if (requirements.length === 0) {
        console.log('✅ No requirements needed, account should be ready');
        return {
          success: true,
          chargesEnabled: currentAccount.charges_enabled,
          payoutsEnabled: currentAccount.payouts_enabled,
          capabilities: currentAccount.capabilities,
          message: 'Account already complete'
        };
      }
      
      // Prepare update data based on requirements
      const updateData = {};
      
      if (requirements.includes('individual.ssn_last_4')) {
        console.log('📝 Adding test SSN...');
        updateData.individual = updateData.individual || {};
        updateData.individual.ssn_last_4 = '0000'; // Test SSN that always approves
      }
      
      if (requirements.includes('individual.id_number')) {
        console.log('📝 Adding test ID number...');
        updateData.individual = updateData.individual || {};
        updateData.individual.id_number = '*********'; // Test ID number
      }
      
      if (requirements.includes('tos_acceptance.date')) {
        console.log('📝 Adding ToS acceptance...');
        updateData.tos_acceptance = {
          date: Math.floor(Date.now() / 1000),
          ip: '127.0.0.1'
        };
      }
      
      // Add other common test requirements
      if (requirements.some(req => req.includes('individual.'))) {
        updateData.individual = updateData.individual || {};
        
        if (requirements.includes('individual.dob.day')) {
          updateData.individual.dob = {
            day: 1,
            month: 1,
            year: 1990
          };
        }
        
        if (requirements.includes('individual.phone')) {
          updateData.individual.phone = '+***********';
        }
        
        if (requirements.includes('individual.address.line1')) {
          updateData.individual.address = {
            line1: '123 Test Street',
            city: 'Test City',
            state: 'CA',
            postal_code: '90210',
            country: 'US'
          };
        }
      }
      
      // Update the account
      if (Object.keys(updateData).length > 0) {
        console.log('🔄 Updating account with test data...');
        await this.stripe.accounts.update(accountId, updateData);
        console.log('✅ Account updated');
      }
      
      // Enable capabilities if not already requested
      await this.enableAccountCapabilities(accountId, ['card_payments', 'transfers']);
      
      // Check final status
      const updatedAccount = await this.stripe.accounts.retrieve(accountId);
      
      return {
        success: true,
        chargesEnabled: updatedAccount.charges_enabled,
        payoutsEnabled: updatedAccount.payouts_enabled,
        capabilities: updatedAccount.capabilities,
        requirements: updatedAccount.requirements.currently_due,
        message: updatedAccount.charges_enabled && updatedAccount.payouts_enabled 
          ? 'Account fully activated' 
          : 'Account updated, capabilities may take a few minutes to activate'
      };
      
    } catch (error) {
      console.error('Error completing account for testing:', error);
      throw error;
    }
  }
}

// Export configured service
const stripeService = new StripeService();
export default stripeService;
