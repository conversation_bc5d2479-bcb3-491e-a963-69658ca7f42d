<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Auth Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }

        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #3a3a3a;
        }

        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #555;
            border-radius: 5px;
            background: #333;
            color: white;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .status.success {
            background: #1b4332;
            border: 1px solid #2d5a3d;
            color: #b7e4c7;
        }

        .status.error {
            background: #590e0e;
            border: 1px solid #8b1538;
            color: #ffb3ba;
        }

        .status.info {
            background: #1e3a5f;
            border: 1px solid #2e5984;
            color: #bfdbfe;
        }

        .config {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
            color: #ccc;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔥 Firebase Auth Debug</h1>

        <div class="config">
            <strong>Firebase Config:</strong><br>
            Project: iconpal-cf925<br>
            Domain: iconpal-cf925.firebaseapp.com<br>
            API Key: AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="Digite seu email">
        </div>

        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="TestPass123!" placeholder="Digite sua senha">
        </div>

        <button onclick="testSignup()" id="signupBtn">🔐 Test Signup</button>
        <button onclick="testLogin()" id="loginBtn">🔑 Test Login</button>
        <button onclick="checkConnection()" id="connectionBtn">🌐 Test Connection</button>

        <div id="status" class="status info">
            Ready to test Firebase Auth...
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
        import {
            getAuth,
            createUserWithEmailAndPassword,
            signInWithEmailAndPassword,
            connectAuthEmulator
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
            authDomain: "iconpal-cf925.firebaseapp.com",
            projectId: "iconpal-cf925",
            storageBucket: "iconpal-cf925.appspot.com",
            messagingSenderId: "887109976546",
            appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        // Make functions global
        window.auth = auth;
        window.createUserWithEmailAndPassword = createUserWithEmailAndPassword;
        window.signInWithEmailAndPassword = signInWithEmailAndPassword;

        function log(message, type = 'info') {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            status.textContent = `[${timestamp}] ${message}`;
            status.className = `status ${type}`;
            console.log(`[${timestamp}] ${message}`);
        }

        window.log = log;

        log('Firebase initialized successfully!', 'success');
    </script>

    <script>
        async function testSignup() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const btn = document.getElementById('signupBtn');

            if (!email || !password) {
                log('Please fill in email and password', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '⏳ Creating account...';

            try {
                log('🔄 Attempting to create user account...', 'info');
                log(`📧 Email: ${email}`, 'info');
                log(`🔑 Password: ${password.replace(/./g, '*')}`, 'info');

                const userCredential = await window.createUserWithEmailAndPassword(window.auth, email, password);
                const user = userCredential.user;

                log(`✅ SUCCESS! User created successfully!
🆔 UID: ${user.uid}
📧 Email: ${user.email}
✅ Email Verified: ${user.emailVerified}
📅 Created: ${user.metadata.creationTime}`, 'success');

            } catch (error) {
                log(`❌ SIGNUP FAILED!
🔥 Code: ${error.code}
💬 Message: ${error.message}
📊 Full Error: ${JSON.stringify(error, null, 2)}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔐 Test Signup';
            }
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const btn = document.getElementById('loginBtn');

            if (!email || !password) {
                log('Please fill in email and password', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '⏳ Signing in...';

            try {
                log('🔄 Attempting to sign in...', 'info');

                const userCredential = await window.signInWithEmailAndPassword(window.auth, email, password);
                const user = userCredential.user;

                log(`✅ LOGIN SUCCESS!
🆔 UID: ${user.uid}
📧 Email: ${user.email}
✅ Email Verified: ${user.emailVerified}
📅 Last Sign In: ${user.metadata.lastSignInTime}`, 'success');

            } catch (error) {
                log(`❌ LOGIN FAILED!
🔥 Code: ${error.code}
💬 Message: ${error.message}
📊 Full Error: ${JSON.stringify(error, null, 2)}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔑 Test Login';
            }
        }

        async function checkConnection() {
            const btn = document.getElementById('connectionBtn');
            btn.disabled = true;
            btn.textContent = '⏳ Checking...';

            try {
                log('🔄 Testing Firebase connection...', 'info');

                // Test basic auth state
                const currentUser = window.auth.currentUser;
                log(`👤 Current User: ${currentUser ? currentUser.email : 'None'}`, 'info');

                // Test auth state listener
                window.auth.onAuthStateChanged((user) => {
                    if (user) {
                        log(`🔄 Auth State: User is signed in (${user.email})`, 'info');
                    } else {
                        log('🔄 Auth State: No user signed in', 'info');
                    }
                });

                // Test network connectivity
                const response = await fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' });
                log('🌐 Network connectivity: OK', 'success');

                // Test Firebase Auth endpoint
                try {
                    const authResponse = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ returnSecureToken: true })
                    });

                    if (authResponse.ok) {
                        log('🔥 Firebase Auth API: Reachable', 'success');
                    } else {
                        log(`🔥 Firebase Auth API: Error ${authResponse.status}`, 'error');
                    }
                } catch (authError) {
                    log(`🔥 Firebase Auth API: Connection failed - ${authError.message}`, 'error');
                }

                log(`✅ CONNECTION TEST COMPLETE!
🔥 Firebase App: ${window.auth.app.name}
🆔 Project ID: ${window.auth.app.options.projectId}
🌐 Auth Domain: ${window.auth.app.options.authDomain}
🔑 API Key: ${window.auth.app.options.apiKey.substring(0, 10)}...`, 'success');

            } catch (error) {
                log(`❌ CONNECTION TEST FAILED!
💬 Error: ${error.message}
📊 Details: ${JSON.stringify(error, null, 2)}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🌐 Test Connection';
            }
        }

        // Auto-run connection test on load
        window.addEventListener('load', () => {
            setTimeout(checkConnection, 1000);
        });
    </script>
</body>

</html>