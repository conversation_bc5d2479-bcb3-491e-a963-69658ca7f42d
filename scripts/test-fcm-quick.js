#!/usr/bin/env node

import http from 'http';
import https from 'https';

console.log('🔧 FCM Quick Test - Identifying hang point...\n');

// Colors for terminal output
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Test 1: Check if servers are running
async function checkServers() {
    log('🔍 Step 1: Checking servers...', 'blue');
    
    const checkServer = (port, name) => {
        return new Promise((resolve) => {
            const req = http.request({
                hostname: 'localhost',
                port: port,
                method: 'GET',
                timeout: 3000
            }, (res) => {
                log(`✅ ${name} server (port ${port}): Running`, 'green');
                resolve(true);
            });
            
            req.on('error', () => {
                log(`❌ ${name} server (port ${port}): Not running`, 'red');
                resolve(false);
            });
            
            req.on('timeout', () => {
                log(`⏱️ ${name} server (port ${port}): Timeout`, 'yellow');
                req.destroy();
                resolve(false);
            });
            
            req.end();
        });
    };
    
    const frontendOk = await checkServer(5773, 'Frontend');
    const backendOk = await checkServer(3001, 'Backend');
    
    return { frontendOk, backendOk };
}

// Test 2: Check Firebase configuration endpoint
async function checkFirebaseConfig() {
    log('\n🔍 Step 2: Checking Firebase configuration...', 'blue');
    
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: 3001,
            path: '/api/notifications/fcm/config',
            method: 'GET',
            timeout: 5000
        }, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const config = JSON.parse(data);
                    if (config.success && config.config) {
                        log('✅ Firebase config endpoint: Working', 'green');
                        log(`📋 API Key: ${config.config.apiKey.substring(0, 20)}...`, 'cyan');
                        log(`📋 Project ID: ${config.config.projectId}`, 'cyan');
                        resolve(true);
                    } else {
                        log('❌ Firebase config endpoint: Invalid response', 'red');
                        log(`Response: ${data}`, 'yellow');
                        resolve(false);
                    }
                } catch (error) {
                    log('❌ Firebase config endpoint: Invalid JSON', 'red');
                    log(`Response: ${data}`, 'yellow');
                    resolve(false);
                }
            });
        });
        
        req.on('error', (error) => {
            log('❌ Firebase config endpoint: Connection failed', 'red');
            log(`Error: ${error.message}`, 'yellow');
            resolve(false);
        });
        
        req.on('timeout', () => {
            log('⏱️ Firebase config endpoint: Timeout', 'yellow');
            req.destroy();
            resolve(false);
        });
        
        req.end();
    });
}

// Test 3: Check service worker files
async function checkServiceWorkerFiles() {
    log('\n🔍 Step 3: Checking service worker files...', 'blue');
    
    const checkFile = (filename) => {
        return new Promise((resolve) => {
            const req = http.request({
                hostname: 'localhost',
                port: 5773,
                path: `/${filename}`,
                method: 'GET',
                timeout: 3000
            }, (res) => {
                if (res.statusCode === 200) {
                    log(`✅ ${filename}: Available`, 'green');
                    resolve(true);
                } else {
                    log(`❌ ${filename}: Status ${res.statusCode}`, 'red');
                    resolve(false);
                }
            });
            
            req.on('error', () => {
                log(`❌ ${filename}: Connection failed`, 'red');
                resolve(false);
            });
            
            req.on('timeout', () => {
                log(`⏱️ ${filename}: Timeout`, 'yellow');
                req.destroy();
                resolve(false);
            });
            
            req.end();
        });
    };
    
    const files = [
        'firebase-messaging-sw.js',
        'firebase-messaging-sw-robust.js',
        'test-fcm-no-hang.html'
    ];
    
    const results = [];
    for (const file of files) {
        const result = await checkFile(file);
        results.push(result);
    }
    
    return results;
}

// Test 4: Check Firebase CDN availability
async function checkFirebaseCDN() {
    log('\n🔍 Step 4: Checking Firebase CDN...', 'blue');
    
    return new Promise((resolve) => {
        const req = https.request({
            hostname: 'www.gstatic.com',
            path: '/firebasejs/10.7.1/firebase-app-compat.js',
            method: 'HEAD',
            timeout: 5000
        }, (res) => {
            if (res.statusCode === 200) {
                log('✅ Firebase CDN: Available', 'green');
                resolve(true);
            } else {
                log(`❌ Firebase CDN: Status ${res.statusCode}`, 'red');
                resolve(false);
            }
        });
        
        req.on('error', (error) => {
            log('❌ Firebase CDN: Connection failed', 'red');
            log(`Error: ${error.message}`, 'yellow');
            resolve(false);
        });
        
        req.on('timeout', () => {
            log('⏱️ Firebase CDN: Timeout', 'yellow');
            req.destroy();
            resolve(false);
        });
        
        req.end();
    });
}

// Main test function
async function runQuickTest() {
    const startTime = Date.now();
    
    try {
        // Run all tests
        const serverStatus = await checkServers();
        const configOk = await checkFirebaseConfig();
        const swFiles = await checkServiceWorkerFiles();
        const cdnOk = await checkFirebaseCDN();
        
        // Summary
        log('\n📊 TEST SUMMARY:', 'blue');
        log('═'.repeat(50), 'blue');
        
        const allPassed = serverStatus.frontendOk && serverStatus.backendOk && configOk && swFiles.every(r => r) && cdnOk;
        
        if (allPassed) {
            log('🎉 All infrastructure tests PASSED!', 'green');
            log('💡 The hang issue might be browser-specific.', 'yellow');
            log('💡 Try the new test page: http://localhost:5773/test-fcm-no-hang.html', 'cyan');
        } else {
            log('❌ Some infrastructure issues found:', 'red');
            
            if (!serverStatus.frontendOk) log('  - Frontend server not running', 'red');
            if (!serverStatus.backendOk) log('  - Backend server not running', 'red');
            if (!configOk) log('  - Firebase config endpoint issues', 'red');
            if (!swFiles.every(r => r)) log('  - Service worker files missing', 'red');
            if (!cdnOk) log('  - Firebase CDN not accessible', 'red');
        }
        
        const duration = Date.now() - startTime;
        log(`\n⏱️ Test completed in ${duration}ms`, 'cyan');
        
        log('\n🔧 NEXT STEPS:', 'blue');
        log('1. Fix any infrastructure issues above', 'yellow');
        log('2. Open: http://localhost:5773/test-fcm-no-hang.html', 'yellow');
        log('3. Click "Clear All Caches" first', 'yellow');
        log('4. Then click "Run Robust FCM Test"', 'yellow');
        
    } catch (error) {
        log(`❌ Test failed: ${error.message}`, 'red');
    }
}

// Run the test
runQuickTest(); 