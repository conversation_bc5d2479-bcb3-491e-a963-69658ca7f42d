#!/usr/bin/env node

import fetch from 'node-fetch';

const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message, status = 'info') {
    const statusIcon = {
        'success': '✅',
        'error': '❌', 
        'warning': '⚠️',
        'info': '🔍'
    };
    const statusColor = {
        'success': 'green',
        'error': 'red',
        'warning': 'yellow', 
        'info': 'cyan'
    };
    
    log(`${statusIcon[status]} [STEP ${step}] ${message}`, statusColor[status]);
}

async function testFCMSetup() {
    log('\n🎯 FCM AUTOMATED TEST - PinPal Project', 'bold');
    log('=' .repeat(50), 'blue');
    
    try {
        // STEP 1: Verificar se os servidores estão rodando
        logStep(1, 'Checking server status...', 'info');
        
        try {
            const backendResponse = await fetch('http://localhost:3001/api/utility/health', {
                timeout: 5000
            });
            if (backendResponse.ok) {
                logStep(1, 'Backend server is running on port 3001', 'success');
            } else {
                throw new Error('Backend health check failed');
            }
        } catch (error) {
            logStep(1, 'Backend server is NOT running on port 3001', 'error');
            log('   💡 Run: node server.js', 'yellow');
            return false;
        }

        try {
            const frontendResponse = await fetch('http://localhost:5773', {
                timeout: 5000
            });
            if (frontendResponse.ok) {
                logStep(1, 'Frontend server is running on port 5773', 'success');
            } else {
                throw new Error('Frontend not accessible');
            }
        } catch (error) {
            logStep(1, 'Frontend server is NOT running on port 5773', 'error');
            log('   💡 Run: npm run dev', 'yellow');
            return false;
        }

        // STEP 2: Verificar configuração Firebase
        logStep(2, 'Testing Firebase configuration endpoint...', 'info');
        
        const configResponse = await fetch('http://localhost:3001/api/notifications/fcm/config');
        const configData = await configResponse.json();
        
        if (!configData.success) {
            logStep(2, 'Failed to load Firebase configuration', 'error');
            return false;
        }
        
        const config = configData.config;
        logStep(2, 'Firebase configuration loaded successfully', 'success');
        log(`   📋 API Key: ${config.apiKey}`, 'cyan');
        log(`   📋 Project ID: ${config.projectId}`, 'cyan');
        log(`   📋 Sender ID: ${config.messagingSenderId}`, 'cyan');
        
        // Verificar se não são valores placeholder
        if (config.apiKey.includes('placeholder') || config.apiKey === 'AIzaSyAJF8mPMpHyPqGhKqFwL8C7dQJxKzNfQ2M') {
            logStep(2, 'WARNING: API Key appears to be a placeholder!', 'warning');
            log('   🔧 Check your .env file for correct Firebase configuration', 'yellow');
            return false;
        }
        
        if (config.messagingSenderId === '123456789012') {
            logStep(2, 'WARNING: Sender ID appears to be a placeholder!', 'warning');
            log('   🔧 Check your .env file for correct Firebase configuration', 'yellow');
            return false;
        }

        // STEP 3: Verificar VAPID key
        logStep(3, 'Testing VAPID key endpoint...', 'info');
        
        const vapidResponse = await fetch('http://localhost:3001/api/notifications/fcm/vapid-key');
        const vapidData = await vapidResponse.json();
        
        if (vapidData.success && vapidData.vapidKey) {
            logStep(3, 'VAPID key endpoint working', 'success');
            log(`   🔑 VAPID Key: ${vapidData.vapidKey.substring(0, 20)}...`, 'cyan');
            
            // Validar formato da VAPID key (mais flexível)
            if (vapidData.vapidKey.length >= 87 && vapidData.vapidKey.startsWith('B')) {
                logStep(3, 'VAPID key format is valid', 'success');
            } else {
                logStep(3, 'VAPID key format is invalid', 'error');
                log(`   📏 Expected: ~88 characters starting with 'B'`, 'yellow');
                log(`   📏 Got: ${vapidData.vapidKey.length} characters`, 'yellow');
            }
        } else {
            logStep(3, 'VAPID key endpoint failed', 'error');
            return false;
        }

        // STEP 4: Verificar service worker
        logStep(4, 'Checking service worker files...', 'info');
        
        try {
            const swResponse = await fetch('http://localhost:5773/firebase-messaging-sw-minimal.js');
            if (swResponse.ok) {
                logStep(4, 'Service worker file is accessible', 'success');
            } else {
                logStep(4, 'Service worker file not found', 'error');
                log('   💡 Make sure firebase-messaging-sw-minimal.js exists in public/', 'yellow');
            }
        } catch (error) {
            logStep(4, 'Could not check service worker file', 'warning');
        }

        // STEP 5: Testar Firebase Installations API com formato correto
        logStep(5, 'Testing Firebase Installations API...', 'info');
        
        try {
            // Formato correto da API Installations (sem "platform")
            const installationData = {
                appId: config.appId,
                sdkVersion: 'w:10.7.1'
            };

            const installationResponse = await fetch(
                `https://firebaseinstallations.googleapis.com/v1/projects/${config.projectId}/installations`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-goog-api-key': config.apiKey
                    },
                    body: JSON.stringify(installationData)
                }
            );

            if (installationResponse.ok) {
                const installationResult = await installationResponse.json();
                logStep(5, 'Firebase Installations API working correctly!', 'success');
                log(`   🆔 Installation ID: ${installationResult.fid?.substring(0, 20)}...`, 'cyan');
            } else {
                const errorText = await installationResponse.text();
                logStep(5, `Firebase Installations API failed: ${installationResponse.status}`, 'error');
                log(`   📋 Error: ${errorText}`, 'red');
                return false;
            }
        } catch (error) {
            logStep(5, `Firebase Installations API test failed: ${error.message}`, 'error');
            return false;
        }

        // RESULTADO FINAL
        log('\n' + '='.repeat(50), 'blue');
        log('🎉 FCM SETUP VALIDATION COMPLETE!', 'bold');
        log('✅ All backend components are working correctly', 'green');
        log('✅ Firebase configuration is valid', 'green');
        log('✅ Firebase Installations API is accessible', 'green');
        log('✅ FCM token generation should work in browser', 'green');
        log('\n📋 Next Steps:', 'bold');
        log('1. Open: http://localhost:5773/test-fcm-final.html', 'cyan');
        log('2. Click "Run Complete FCM Test"', 'cyan');
        log('3. FCM token should generate successfully!', 'cyan');
        
        return true;

    } catch (error) {
        log('\n💥 UNEXPECTED ERROR:', 'red');
        log(error.message, 'red');
        return false;
    }
}

// Executar o teste
testFCMSetup().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    log('\n💥 SCRIPT ERROR:', 'red');
    log(error.message, 'red');
    process.exit(1);
});
