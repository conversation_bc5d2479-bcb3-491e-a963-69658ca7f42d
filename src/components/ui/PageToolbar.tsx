import { FC, ReactNode } from 'react';
import clsx from 'clsx';

interface PageToolbarProps {
  /**
   * Elements rendered on the left side (search, filters, etc.)
   */
  left: ReactNode;
  /**
   * Elements rendered on the right side (primary page actions)
   */
  right?: ReactNode;
  /**
   * Sticky on top of the viewport (default: true)
   */
  sticky?: boolean;
  /**
   * Additional classes for outer wrapper
   */
  className?: string;
}

/**
 * Generic page-level toolbar that keeps controls aligned consistently.
 * Left slot is flexible and wraps; right slot is aligned to the far right.
 */
export const PageToolbar: FC<PageToolbarProps> = ({
  left,
  right,
  sticky = true,
  className = '',
}) => {
  return (
    <div
      className={clsx(
        'flex flex-col sm:flex-row sm:items-center gap-4',
        sticky && 'sticky top-0 z-50 bg-white/90 dark:bg-black/90 backdrop-blur-sm',
        className
      )}
    >
      {/* left controls */}
      <div className="flex-1 flex flex-wrap items-center gap-3 min-w-0">{left}</div>

      {/* right actions */}
      {right && <div className="flex items-center gap-2 ml-auto">{right}</div>}
    </div>
  );
}; 