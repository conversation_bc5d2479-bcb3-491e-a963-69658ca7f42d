import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { classNames } from '@/utils/classNames';
import { Button } from '@/components/ui/design-system/Button';

interface ModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback for when the modal should close */
  onClose: () => void;
  /** Modal title */
  title?: React.ReactNode;
  /** Modal content */
  children: React.ReactNode;
  /** Optional footer content */
  footer?: React.ReactNode;
  /** Max width of the modal */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  /** Whether to hide the close button */
  hideCloseButton?: boolean;
  /** Whether to use glass morphism effect */
  glassEffect?: boolean;
}

/**
 * A reusable modal component built on top of HeadlessUI Dialog
 * Now with glass morphism effect by default
 */
export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  maxWidth = 'md',
  hideCloseButton = false,
  glassEffect = true
}) => {
  const maxWidthClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    'full': 'max-w-full'
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[9999]" onClose={onClose}>
        {/* Backdrop */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 dark:bg-black/50 dark:bg-black/50/50/50/50 transition-opacity" />
        </Transition.Child>

        {/* Modal panel */}
        <div className="fixed inset-0 overflow-y-auto scrollbar-theme">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter={glassEffect ? "ease-out duration-500" : "ease-out duration-300"}
              enterFrom={glassEffect ? "opacity-0 scale-90" : "opacity-0 scale-95"}
              enterTo={glassEffect ? "opacity-100 scale-100" : "opacity-100 scale-100"}
              leave={glassEffect ? "ease-in duration-300" : "ease-in duration-200"}
              leaveFrom={glassEffect ? "opacity-100 scale-100" : "opacity-100 scale-100"}
              leaveTo={glassEffect ? "opacity-0 scale-90" : "opacity-0 scale-95"}
            >
              <Dialog.Panel
                className={classNames(
                  'transform overflow-hidden text-left align-middle transition-all',
                  glassEffect ? 'duration-500' : '',
                  // Rounded corners
                  'rounded-2xl shadow-2xl',
                  // Padding - only for non-full width modals
                  maxWidth !== 'full' ? 'p-6' : 'p-0',
                  // Max width
                  maxWidthClasses[maxWidth],
                  // Full width specific styles
                  maxWidth === 'full' ? 'w-auto h-auto' : 'w-full',
                  // Background e border - estilo limpo como na imagem de referência
                  !glassEffect && 'bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700'
                )}
                style={glassEffect ? {
                  // EFEITO DE VIDRO LÍQUIDO NO BACKGROUND DO MODAL
                  background: 'rgba(255, 255, 255, 0.15)',
                  backdropFilter: 'blur(20px) saturate(180%)',
                  WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                  // BORDA COM EFEITO DE REFLEXO
                  border: '1px solid transparent',
                  backgroundImage: `
                    linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)),
                    linear-gradient(135deg,
                      rgba(255, 255, 255, 0.6) 0%,
                      rgba(255, 255, 255, 0.2) 25%,
                      rgba(255, 255, 255, 0.1) 50%,
                      rgba(255, 255, 255, 0.3) 75%,
                      rgba(255, 255, 255, 0.7) 100%
                    )
                  `,
                  backgroundOrigin: 'border-box',
                  backgroundClip: 'content-box, border-box',
                  boxShadow: `
                    0 8px 32px 0 rgba(31, 38, 135, 0.37),
                    inset 0 1px 0 0 rgba(255, 255, 255, 0.5),
                    0 0 0 1px rgba(255, 255, 255, 0.1)
                  `,
                } : undefined}
              >
                {/* Header with title and close button */}
                {(title || !hideCloseButton) && (
                  <div className={classNames(
                    "flex items-center justify-between",
                    maxWidth !== 'full' ? 'mb-4' : 'mb-0'
                  )}>
                    {title && (
                      <Dialog.Title
                        as="h3"
                        className={glassEffect 
                          ? "text-xl font-semibold text-gray-900 dark:text-slate-100"
                          : "text-xl font-semibold text-gray-900 dark:text-gray-100"
                        }
                      >
                        {title}
                      </Dialog.Title>
                    )}
                    
                    {!hideCloseButton && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClose}
                        className="rounded-full p-2 h-auto min-h-0"
                        aria-label="Close modal"
                      >
                        <XMarkIcon className="w-5 h-5" />
                      </Button>
                    )}
                  </div>
                )}

                {/* Modal content */}
                <div className={maxWidth !== 'full' ? 'mt-2' : ''}>
                  {children}
                </div>

                {/* Footer if provided */}
                {footer && (
                  <div className={classNames(
                    "mt-6",
                    glassEffect ? "pt-4 border-t border-gray-300/30 dark:border-slate-600/30" : "pt-4 border-t border-gray-600"
                  )}>
                    {footer}
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}; 