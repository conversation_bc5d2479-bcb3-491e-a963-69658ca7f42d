import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { classNames } from '@/utils/classNames';
import { Button } from '@/components/ui/design-system/Button';

interface GlassModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback for when the modal should close */
  onClose: () => void;
  /** Modal title */
  title?: React.ReactNode;
  /** Modal content */
  children: React.ReactNode;
  /** Optional footer content */
  footer?: React.ReactNode;
  /** Max width of the modal */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  /** Whether to hide the close button */
  hideCloseButton?: boolean;
  /** Custom className for the modal panel */
  className?: string;
  /** Z-index for the modal (default: z-50) */
  zIndex?: 'z-40' | 'z-50' | 'z-60' | 'z-70' | 'z-80';
  /** Modal variant - glass for glass morphism, clean for solid background like the reference image */
  variant?: 'glass' | 'clean';
}

/**
 * A modal component with glass morphism or clean solid background
 * Based on the design references with backdrop blur and border
 */
export const GlassModal: React.FC<GlassModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  maxWidth = 'md',
  hideCloseButton = false,
  className,
  zIndex = 'z-50',
  variant = 'glass'
}) => {
  const maxWidthClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    'full': 'max-w-full'
  };

  return (
    <Transition.Root appear show={isOpen} as={Fragment}>
      <Dialog as="div" className={`relative ${zIndex}`} onClose={() => {
        // Don't auto-close on outside click - only close via explicit button clicks
        // This prevents accidental closing when clicking on modal content
      }}>
        {/* Backdrop com animação simétrica */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 dark:bg-black/50 dark:bg-black/50/50/50/80" style={{ backdropFilter: 'blur(2px)' }} />
        </Transition.Child>

        {/* Modal panel com animação simétrica - entrada e saída espelhadas */}
        <div className="fixed inset-0 overflow-y-auto scrollbar-theme">
          <div className="flex min-h-full items-center justify-center p-4 text-center overflow-visible">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95 translate-y-4"
              enterTo="opacity-100 scale-100 translate-y-0"
              leave="ease-in duration-300"
              leaveFrom="opacity-100 scale-100 translate-y-0"
              leaveTo="opacity-0 scale-95 translate-y-4"
            >
              <Dialog.Panel
                className={classNames(
                  // Base styles
                  'w-full transform overflow-visible text-left align-middle',
                  // Transição suave
                  'transition-all',
                  // Rounded corners
                  'rounded-2xl',
                  // Enhanced shadow for depth
                  'shadow-2xl',
                  // Padding
                  'p-6',
                  // Max width
                  maxWidthClasses[maxWidth],
                  // Variant-specific styles
                  variant === 'glass'
                    ? 'glass-effect glass-border-reflection glass-modal-panel'
                    : 'clean-modal-panel',
                  // Custom className
                  className
                )}
                style={{ zIndex: 9999 }}
              >
                {/* Header with title and close button */}
                {(title || !hideCloseButton) && (
                  <div className="flex items-center justify-between mb-6">
                    {title && (
                      <Dialog.Title
                        as="h3"
                        className={classNames(
                          "text-xl font-semibold",
                          variant === 'glass'
                            ? "text-gray-900 dark:text-slate-100"
                            : "text-gray-900 dark:text-white"
                        )}
                      >
                        {title}
                      </Dialog.Title>
                    )}
                    
                    {!hideCloseButton && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClose}
                        className="rounded-full p-2 h-auto min-h-0 text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-gray-900 dark:text-white hover:bg-white/20 dark:hover:bg-white/10 hover:scale-110 active:scale-95"
                        aria-label="Close modal"
                      >
                        <XMarkIcon className="w-5 h-5" />
                      </Button>
                    )}
                  </div>
                )}

                {/* Modal content */}
                <div className="mt-2">
                  {children}
                </div>

                {/* Footer if provided */}
                {footer && (
                  <div className="mt-6 pt-4 border-t border-gray-300/30 dark:border-slate-600/30">
                    {footer}
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}; 