import React from 'react';
import { twMerge } from 'tailwind-merge';
import { formFieldTokens } from '../foundations/tokens';

export type InputSize = 'sm' | 'md' | 'lg';
export type InputVariant = 'default' | 'auth' | 'glass';

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Input size variant
   */
  size?: InputSize;
  /**
   * Input style variant
   */
  variant?: InputVariant;
  /**
   * Additional wrapper className
   */
  wrapperClassName?: string;
  /**
   * Error message to display
   */
  error?: string;
  /**
   * Help text displayed below the input
   */
  helpText?: string;
  /**
   * Left icon element
   */
  leftIcon?: React.ReactNode;
  /**
   * Right icon/action element
   */
  rightIcon?: React.ReactNode;
  /**
   * Input label
   */
  label?: string;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      size = 'md',
      variant = 'default',
      wrapperClassName,
      error,
      helpText,
      leftIcon,
      rightIcon,
      label,
      id,
      disabled,
      style,
      ...props
    },
    ref
  ) => {
    // Generate an ID if one isn't provided
    const inputId = id || React.useId();

    // Build input classes based on variant and size
    const baseClasses = 'w-full border rounded-md transition-all duration-200 focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed';
    
    // Usar formFieldTokens para alturas consistentes
    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base', 
      lg: 'px-5 py-3 text-lg'
    };
    
    const variantClasses = {
      default: 'focus:ring-blue-500/20',
      auth: 'focus:ring-blue-500/20',
      glass: 'backdrop-blur-sm focus:ring-blue-500/20'
    };
    
    const errorClasses = error ? 'focus:ring-red-500/20' : '';
    
    const paddingAdjustments = leftIcon ? (size === 'sm' ? 'pl-8' : size === 'lg' ? 'pl-12' : 'pl-10') : '';
    const rightPaddingAdjustments = rightIcon ? (size === 'sm' ? 'pr-8' : size === 'lg' ? 'pr-12' : 'pr-10') : '';
    
    const finalInputClasses = twMerge(
      baseClasses,
      sizeClasses[size],
      variantClasses[variant],
      errorClasses,
      paddingAdjustments,
      rightPaddingAdjustments
    );

    // Classes CSS para diferentes variantes
    const getVariantClasses = (): string => {
      if (error) {
        return 'bg-white dark:bg-gray-900 border-red-600 text-gray-900 dark:text-white';
      }

      switch (variant) {
        case 'glass':
          return 'bg-white/10 border-white/20 text-white placeholder-white/50';
        case 'auth':
          return 'bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400';
        default:
          return 'bg-white dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400';
      }
    };

    // Estilos inline apenas para altura
    const getInputStyles = (): React.CSSProperties => {
      return {
        height: formFieldTokens.height[size],
        ...style
      };
    };

    return (
      <div className={twMerge('flex flex-col space-y-1.5', wrapperClassName)}>
        {label && (
          <label
            htmlFor={inputId}
            className={twMerge(
              'block text-sm font-medium mb-1',
              variant === 'glass' ? 'text-white dark:text-white' : 'text-gray-700 dark:text-gray-300'
            )}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className={twMerge(
              'absolute inset-y-0 left-0 flex items-center pointer-events-none',
              size === 'sm' ? 'left-2' : size === 'lg' ? 'left-4' : 'left-3',
              variant === 'glass' ? "text-white/60 dark:text-white/50" :
              variant === 'auth' ? "text-gray-600 dark:text-gray-300" :
              "text-gray-400 dark:text-gray-500"
            )}>
              <span className={size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'}>
                {leftIcon}
              </span>
            </div>
          )}

          <input
            id={inputId}
            ref={ref}
            disabled={disabled}
            className={twMerge(finalInputClasses, className)}
            style={getInputStyles()}
            {...props}
          />

          {rightIcon && (
            <div className={twMerge(
              'absolute inset-y-0 right-0 flex items-center',
              size === 'sm' ? 'right-2' : size === 'lg' ? 'right-4' : 'right-3',
              variant === 'glass' ? "text-white/60 dark:text-white/50" :
              variant === 'auth' ? "text-gray-600 dark:text-gray-300" :
              "text-gray-400 dark:text-gray-500"
            )}>
              <span className={size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'}>
                {rightIcon}
              </span>
            </div>
          )}
        </div>

        {(error || helpText) && (
          <p className={twMerge(
            "mt-1 text-sm",
            error ? "text-red-600 dark:text-red-400" : variant === 'glass' ? "text-white/70 dark:text-white/60" : "text-gray-600 dark:text-gray-400"
          )}>
            {error || helpText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input'; 