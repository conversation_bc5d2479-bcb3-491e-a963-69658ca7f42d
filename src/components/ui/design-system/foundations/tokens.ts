// ===== FORM FIELD TOKENS =====
export const formFieldTokens = {
  // Alturas padronizadas para todos os campos
  height: {
    sm: '32px',   // h-8
    md: '40px',   // h-10 - padrão
    lg: '48px',   // h-12
  },
  
  // Cores de fundo padronizadas (Light Mode como padrão)
  background: {
    default: '#ffffff',      // bg-white - fundo padrão para campos no light mode
    hover: '#f3f4f6',        // bg-gray-100 - hover state light mode
    disabled: '#f9fafb',     // bg-gray-50 - disabled state light mode
    glass: 'rgba(255, 255, 255, 0.1)', // bg-white/10 - glass variant (mantido para dark mode)
  },

  // Cores de borda padronizadas (Light Mode como padrão)
  border: {
    default: '#d1d5db',      // border-gray-300 - borda padrão light mode
    focus: '#2563eb',        // border-blue-600 - estado de foco (mantido)
    error: '#dc2626',        // border-red-600 - estado de erro (mantido)
    glass: 'rgba(255, 255, 255, 0.2)', // border-white/20 - glass variant (mantido para dark mode)
    divide: '#e5e7eb',       // border-gray-200 - divisões light mode
  },

  // Cores de texto padronizadas (Light Mode como padrão)
  text: {
    primary: '#111827',      // text-gray-900 - texto principal light mode
    placeholder: '#6b7280',  // text-gray-500 - placeholder light mode
    disabled: '#9ca3af',     // text-gray-400 - disabled light mode
    glass: '#ffffff',        // text-white - glass variant (mantido para dark mode)
    glassPlaceholder: 'rgba(255, 255, 255, 0.5)', // glass placeholder (mantido para dark mode)
  }
}; 