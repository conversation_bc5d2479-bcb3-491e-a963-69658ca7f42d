/**
 * CSS Variables Utilities
 * 
 * Generate and manage CSS custom properties from design tokens
 * Provides runtime token injection and theme switching
 */

import { colorTokens, primitiveColors, getColorToken } from '../tokens/colors';
import { spacingTokens, getSpacingCSSVars } from '../tokens/spacing';
import { borderTokens, getBorderCSSVars } from '../tokens/borders';
import { shadowTokens, getShadowCSSVars } from '../tokens/shadows';
import { motionTokens, getMotionCSSVars } from '../tokens/motion';
import { typographyTokens } from '../tokens/typography';
import { breakpointTokens, getBreakpointCSSVars } from '../tokens/breakpoints';
import { zIndexTokens, getZIndexCSSVars } from '../tokens/z-index';

// ===== CSS VARIABLE GENERATION =====
export const generateColorCSSVars = () => {
  const cssVars: Record<string, string> = {};

  // Primitive colors
  Object.entries(primitiveColors).forEach(([family, shades]) => {
    Object.entries(shades).forEach(([shade, value]) => {
      cssVars[`--color-${family}-${shade}`] = value;
    });
  });

  // Semantic color tokens
  const processColorTokens = (tokens: any, prefix: string = '') => {
    Object.entries(tokens).forEach(([key, value]) => {
      const varName = prefix ? `--color-${prefix}-${key}` : `--color-${key}`;
      
      if (typeof value === 'object' && value !== null) {
        processColorTokens(value, prefix ? `${prefix}-${key}` : key);
      } else {
        cssVars[varName] = value as string;
      }
    });
  };

  processColorTokens(colorTokens);
  return cssVars;
};

export const generateSpacingCSSVars = () => {
  return getSpacingCSSVars();
};

export const generateTypographyCSSVars = () => {
  const cssVars: Record<string, string> = {};

  const processTypographyTokens = (tokens: any, prefix: string = '') => {
    Object.entries(tokens).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        if ('fontSize' in value) {
          // This is a typography token object
          const typographyValue = value as any;
          cssVars[`--typography-${prefix}-${key}-font-size`] = typographyValue.fontSize;
          cssVars[`--typography-${prefix}-${key}-line-height`] = typographyValue.lineHeight;
          cssVars[`--typography-${prefix}-${key}-font-weight`] = typographyValue.fontWeight;
          cssVars[`--typography-${prefix}-${key}-letter-spacing`] = typographyValue.letterSpacing;
          cssVars[`--typography-${prefix}-${key}-font-family`] = Array.isArray(typographyValue.fontFamily) 
            ? typographyValue.fontFamily.join(', ') 
            : typographyValue.fontFamily;
        } else {
          // Nested object, recurse
          processTypographyTokens(value, prefix ? `${prefix}-${key}` : key);
        }
      }
    });
  };

  processTypographyTokens(typographyTokens);
  return cssVars;
};

// ===== COMPREHENSIVE CSS VARIABLES =====
export const generateAllCSSVars = () => {
  return {
    ...generateColorCSSVars(),
    ...generateSpacingCSSVars(),
    ...getBorderCSSVars(),
    ...getShadowCSSVars(),
    ...getMotionCSSVars(),
    ...generateTypographyCSSVars(),
    ...getBreakpointCSSVars(),
    ...getZIndexCSSVars(),
  };
};

// ===== CSS INJECTION =====
export const injectCSSVars = (vars: Record<string, string>, target: HTMLElement = document.documentElement) => {
  Object.entries(vars).forEach(([property, value]) => {
    target.style.setProperty(property, value);
  });
};

export const injectAllTokens = (target: HTMLElement = document.documentElement) => {
  const allVars = generateAllCSSVars();
  injectCSSVars(allVars, target);
};

// ===== CSS STRING GENERATION =====
export const generateCSSString = (vars: Record<string, string>, selector: string = ':root') => {
  const cssRules = Object.entries(vars)
    .map(([property, value]) => `  ${property}: ${value};`)
    .join('\n');
  
  return `${selector} {\n${cssRules}\n}`;
};

export const generateAllTokensCSS = (selector: string = ':root') => {
  const allVars = generateAllCSSVars();
  return generateCSSString(allVars, selector);
};

// ===== THEME SWITCHING =====
export const createThemeCSS = (
  themeName: string,
  colorOverrides: Record<string, string>,
  selector?: string
) => {
  const baseVars = generateAllCSSVars();
  const themeVars = { ...baseVars };

  // Apply color overrides
  Object.entries(colorOverrides).forEach(([tokenPath, value]) => {
    const cssVar = `--color-${tokenPath.replace(/\./g, '-')}`;
    themeVars[cssVar] = value;
  });

  const themeSelector = selector || `[data-theme="${themeName}"]`;
  return generateCSSString(themeVars, themeSelector);
};

export const switchTheme = (
  themeName: string,
  colorOverrides: Record<string, string>,
  target: HTMLElement = document.documentElement
) => {
  // Set theme attribute
  target.setAttribute('data-theme', themeName);
  
  // Apply color overrides
  Object.entries(colorOverrides).forEach(([tokenPath, value]) => {
    const cssVar = `--color-${tokenPath.replace(/\./g, '-')}`;
    target.style.setProperty(cssVar, value);
  });
};

// ===== RUNTIME TOKEN ACCESS =====
export const getCSSVar = (property: string, target: HTMLElement = document.documentElement): string => {
  return getComputedStyle(target).getPropertyValue(property).trim();
};

export const setCSSVar = (property: string, value: string, target: HTMLElement = document.documentElement) => {
  target.style.setProperty(property, value);
};

export const removeCSSVar = (property: string, target: HTMLElement = document.documentElement) => {
  target.style.removeProperty(property);
};

// ===== TOKEN VALIDATION =====
export const validateCSSVars = (requiredVars: string[], target: HTMLElement = document.documentElement): Record<string, boolean> => {
  const results: Record<string, boolean> = {};
  
  requiredVars.forEach(varName => {
    const value = getCSSVar(varName, target);
    results[varName] = value !== '';
  });

  return results;
};

// ===== CRITICAL CSS INJECTION =====
export const injectCriticalCSS = () => {
  const criticalVars = {
    // Essential colors
    '--color-surface-primary': colorTokens.surface.primary,
    '--color-surface-elevated': colorTokens.surface.elevated,
    '--color-text-primary': colorTokens.text.primary,
    '--color-text-secondary': colorTokens.text.secondary,
    '--color-border-default': colorTokens.border.default,
    
    // Essential spacing
    '--spacing-2': '0.5rem',
    '--spacing-4': '1rem',
    '--spacing-6': '1.5rem',
    
    // Essential shadows
    '--shadow-sm': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    '--shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    
    // Card system
    '--card-primary-bg': colorTokens.surface.elevated,
    '--card-primary-border': colorTokens.border.default,
    '--card-secondary-bg': colorTokens.surface.interactive,
    '--card-secondary-border': colorTokens.border.strong,
    '--card-interactive-bg': colorTokens.surface.secondary,
    '--card-interactive-border': colorTokens.border.interactive,
  };

  // Create style element
  const styleElement = document.createElement('style');
  styleElement.id = 'design-system-critical-css';
  
  const criticalCSS = `
    /* Light mode (default) */
    :root {
      --color-surface-primary: #ffffff;
      --color-surface-elevated: #f5f5f5;
      --color-text-primary: #171717;
      --color-text-secondary: #6b7280;
      --color-border-default: #d4d4d4;
      --card-primary-bg: #ffffff;
      --card-primary-border: #e5e5e5;
      --card-secondary-bg: #f8fafc;
      --card-secondary-border: #e2e8f0;
      --card-interactive-bg: #f1f5f9;
      --card-interactive-border: #cbd5e1;
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    /* Dark mode */
    .dark {
      --color-surface-primary: #000000;
      --color-surface-elevated: #171717;
      --color-text-primary: #ffffff;
      --color-text-secondary: #9ca3af;
      --color-border-default: #404040;
      --card-primary-bg: #1a1a1a;
      --card-primary-border: #3f3f46;
      --card-secondary-bg: #262626;
      --card-secondary-border: #525252;
      --card-interactive-bg: #404040;
      --card-interactive-border: #737373;
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }
    
    .card-primary {
      background-color: var(--card-primary-bg);
      border: 1px solid var(--card-primary-border);
    }
    
    .card-secondary {
      background-color: var(--card-secondary-bg);
      border: 1px solid var(--card-secondary-border);
    }
    
    .card-interactive {
      background-color: var(--card-interactive-bg);
      border: 1px solid var(--card-interactive-border);
    }
    
    .card-primary:hover,
    .card-secondary:hover,
    .card-interactive:hover {
      box-shadow: var(--shadow-md);
    }
    
    .modern-background {
      background-color: var(--color-surface-primary);
    }
    
    .modern-text-primary {
      color: var(--color-text-primary);
    }
  `;
  
  styleElement.textContent = criticalCSS;
  
  // Insert at the end of head to avoid overriding Tailwind classes
  const head = document.head;
  head.appendChild(styleElement);
};

// ===== INITIALIZATION =====
export const initializeDesignSystem = () => {
  // Inject critical CSS first
  injectCriticalCSS();
  
  // Then inject all tokens
  injectAllTokens();
  
  console.log('🎨 Design System initialized with all tokens');
}; 