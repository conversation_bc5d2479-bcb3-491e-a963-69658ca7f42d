import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { twMerge } from 'tailwind-merge';
import { Button } from '../Button';

export type ModalSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
export type ModalVariant = 'default' | 'glass';

export interface ModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Callback for when the modal should close */
  onClose: () => void;
  /** Modal title */
  title?: React.ReactNode;
  /** Modal content */
  children: React.ReactNode;
  /** Optional footer content */
  footer?: React.ReactNode;
  /** Size of the modal */
  size?: ModalSize;
  /** Visual variant of the modal */
  variant?: ModalVariant;
  /** Whether to hide the close button */
  hideCloseButton?: boolean;
  /** Whether clicking outside closes the modal */
  closeOnOverlayClick?: boolean;
  /** Custom className for the modal panel */
  className?: string;
  /** Custom className for the backdrop */
  backdropClassName?: string;
}

/**
 * A standardized modal component for the design system
 * Supports both default and glass morphism variants
 */
export const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
  (
    {
      isOpen,
      onClose,
      title,
      children,
      footer,
      size = 'md',
      variant = 'default',
      hideCloseButton = false,
      closeOnOverlayClick = true,
      className,
      backdropClassName,
    },
    ref
  ) => {
    const sizeClasses = {
      xs: 'max-w-xs',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      full: 'max-w-full w-full h-full',
    };

    const variantStyles = {
      default: {
        panel: [
          // Fundo branco limpo no light mode, escuro no dark mode
          'bg-white dark:bg-gray-900',
          // Borda sutil e arredondada
          'border border-gray-200 dark:border-gray-700',
          // Sombra suave como na imagem de referência
          'shadow-2xl',
        ].join(' '),
        backdrop: 'bg-black/50',
        title: 'text-gray-900 dark:text-white',
      },
      glass: {
        panel: [
          'bg-white/10 dark:bg-white/5',
          'backdrop-blur-xl',
          'border border-white/20 dark:border-white/10',
          'shadow-2xl',
        ].join(' '),
        backdrop: 'bg-black/60 backdrop-blur-sm',
        title: 'text-gray-900 dark:text-white',
      },
    };

    const handleClose = closeOnOverlayClick ? onClose : () => {};

    return (
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={handleClose}>
          {/* Backdrop */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div
              className={twMerge(
                'fixed inset-0 transition-opacity',
                variantStyles[variant].backdrop,
                backdropClassName
              )}
            />
          </Transition.Child>

          {/* Modal panel */}
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel
                  ref={ref}
                  className={twMerge(
                    // Base styles
                    'w-full transform overflow-hidden text-left align-middle transition-all',
                    // Size
                    sizeClasses[size],
                    // Variant styles
                    variantStyles[variant].panel,
                    // Layout
                    size === 'full' ? 'rounded-none' : 'rounded-xl',
                    size === 'full' ? 'p-0' : 'p-6',
                    // Custom className
                    className
                  )}
                >
                  {/* Header */}
                  {(title || !hideCloseButton) && (
                    <div className={twMerge(
                      'flex items-center justify-between',
                      size === 'full' ? 'p-6 border-b border-gray-600' : 'mb-4'
                    )}>
                      {title && (
                        <Dialog.Title
                          as="h3"
                          className={twMerge(
                            'text-lg font-semibold leading-6',
                            variantStyles[variant].title
                          )}
                        >
                          {title}
                        </Dialog.Title>
                      )}

                      {!hideCloseButton && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={onClose}
                          className="rounded-full p-2 h-auto min-h-0"
                          aria-label="Close modal"
                        >
                          <XMarkIcon className="w-5 h-5" />
                        </Button>
                      )}
                    </div>
                  )}

                  {/* Content */}
                  <div className={size === 'full' ? 'p-6 flex-1' : ''}>
                    {children}
                  </div>

                  {/* Footer */}
                  {footer && (
                    <div className={twMerge(
                      'mt-6 pt-4',
                      size === 'full' ? 'px-6 pb-6 border-t border-gray-600' : 'border-t border-gray-600',
                      variant === 'glass' ? 'border-white/20 dark:border-white/10' : ''
                    )}>
                      {footer}
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  }
);

Modal.displayName = 'Modal'; 