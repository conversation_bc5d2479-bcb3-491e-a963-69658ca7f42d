import { useState, useRef, useEffect } from 'react';
import type { User } from '@/types';
import { Link, useNavigate } from 'react-router-dom';
import { Avatar } from '../ui/Avatar';
import { Button } from '@/components/ui/design-system/Button';

import { useMessageStore, Conversation, getUserInfo } from '@/store/messageStore';
import { classNames } from '@/utils/classNames';
import { useTheme } from '@/theme/ThemeProvider';
import { UserIcon, ChatBubbleLeftRightIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon, MoonIcon, SunIcon, ShieldCheckIcon, BellIcon, QuestionMarkCircleIcon, ClockIcon, BookmarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '@/store/authStore';
import { useNotifications } from '@/hooks/useNotifications';
import { userNameUtils } from '@/utils/nameUtils';
import { profileUtils } from '@/utils/profileUtils';

export interface HeaderProps {
  user?: User | null;
  onLogout?: () => Promise<void>;
  onMenuClick?: () => void;
}



export const Header = ({ user, onLogout, onMenuClick }: HeaderProps) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [messagesOpen, setMessagesOpen] = useState(false);

  const menuRef = useRef<HTMLDivElement>(null);
  const messagesRef = useRef<HTMLDivElement>(null);
  const unreadCount = useMessageStore((state) => state.getUnreadCount());
  const { conversations, setActiveConversation } = useMessageStore();
  const navigate = useNavigate();
  const { actualTheme } = useTheme();
  const { permissions } = useAuthStore();
  
  // Notifications state
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const { notifications, unreadCount: notificationUnreadCount, markAsRead, markAllAsRead } = useNotifications(user?.id || '');

  // REMOVIDO: console.log que causava spam nos logs

  // Function to get other participant in a conversation
  const getOtherParticipant = (conversation: Conversation) => {
    const currentUserId = 'current-user'; // This should ideally come from auth store
    const otherParticipantId = conversation.participants.find(id => id !== currentUserId);
    return otherParticipantId ? getUserInfo(otherParticipantId) : null;
  };
  
  // Function to format date for display
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    
    // Today - show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    // Within last week - show day name
    const daysDiff = Math.round((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    }
    
    // Older - show date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  // Sort conversations by the timestamp of the last message and unread first
  const sortedConversations = [...conversations].sort((a, b) => {
    // Unread conversations first
    if (a.unreadCount > 0 && b.unreadCount === 0) return -1;
    if (a.unreadCount === 0 && b.unreadCount > 0) return 1;
    
    // Then by timestamp
    const timestampA = a.lastMessage?.timestamp ?? '0';
    const timestampB = b.lastMessage?.timestamp ?? '0';
    return new Date(timestampB).getTime() - new Date(timestampA).getTime();
  });

  // Limit to the most recent 5 conversations
  const recentConversations = sortedConversations.slice(0, 5);
  
  const handleConversationClick = (conversationId: string) => {
    setMessagesOpen(false);
    setActiveConversation(conversationId);
    navigate('/messages');
  };

  const handleViewAllMessages = () => {
    setMessagesOpen(false);
    navigate('/messages');
  };

  const handleNewMessage = () => {
    navigate('/messages', { state: { openNewMessageModal: true } });
  };

  const handleNotificationClick = (notification: any) => {
    markAsRead(notification.id);
    setNotificationsOpen(false);
    
    // Navigate based on notification type
    if (notification.pinId) {
      // TODO: Navigate to pin detail
      console.log('Navigate to pin:', notification.pinId);
    }
  };

  const handleViewAllNotifications = () => {
    setNotificationsOpen(false);
    navigate('/notifications');
  };

  const { toggleTheme } = useTheme();

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
      if (messagesRef.current && !messagesRef.current.contains(event.target as Node)) {
        setMessagesOpen(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuOpen, messagesOpen, notificationsOpen]);

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 transition-colors duration-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <button
              className="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
              onClick={onMenuClick}
            >
              <svg
                className="w-6 h-6"
                fill="none"
                strokeWidth="1.5"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
              </svg>
            </button>
            <Link to="/" className="flex items-center">
              <img src="/images/system/pinpal-logo.png" alt="PinPal logo" width={80} height={32} className="flex-shrink-0" />
            </Link>
            {/* Development mode indicator */}
            {import.meta.env.DEV && (
              <span className="ml-3 px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded-full border">
                DEV
              </span>
            )}
          </div>
          <div className="flex items-center">
            {/* Theme toggle button */}
            <button
              onClick={toggleTheme}
              className="mr-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-500 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
              aria-label="Toggle theme"
            >
              {actualTheme === 'dark' ? (
                <SunIcon className="w-6 h-6" />
              ) : (
                <MoonIcon className="w-6 h-6" />
              )}
            </button>
            
            {/* Notifications icon with badge and dropdown */}
            {user && (
              <div className="relative" ref={notificationsRef}>
                <button
                  onClick={() => setNotificationsOpen(!notificationsOpen)}
                  className="mr-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-500 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 relative"
                >
                  <BellIcon className="w-6 h-6" />
                  {notificationUnreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center pulse-animation">
                      {notificationUnreadCount}
                    </span>
                  )}
                </button>
                
                {notificationsOpen && (
                  <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-50 dark:bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-custom-gray-800 rounded-xl shadow-lg z-50 animate-fade-in">
                    <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-700 flex justify-between items-center">
                      <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Notifications</h3>
                      {notificationUnreadCount > 0 && (
                        <button
                          onClick={markAllAsRead}
                          className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                        >
                          Mark all as read
                        </button>
                      )}
                    </div>
                    
                    <div className="max-h-96 overflow-y-auto">
                      {notifications.length === 0 ? (
                        <div className="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                          No notifications yet
                        </div>
                      ) : (
                        <ul className="divide-y divide-gray-100 dark:divide-gray-700">
                          {notifications.slice(0, 10).map(notification => (
                            <li 
                              key={notification.id}
                              onClick={() => handleNotificationClick(notification)}
                              className={classNames(
                                "p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors relative",
                                !notification.isRead ? "bg-blue-50/30 dark:bg-blue-900/20" : ""
                              )}
                            >
                              <div className="flex items-start gap-3">
                                <div className={classNames(
                                  "w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 flex-shrink-0",
                                  !notification.isRead ? "pulse-animation" : ""
                                )}>
                                  {notification.type === 'mention' && '💬'}
                                  {notification.type === 'like' && '❤️'}
                                  {notification.type === 'follow' && '👤'}
                                  {notification.type === 'reply' && '↩️'}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex justify-between items-baseline">
                                    <h4 className={classNames(
                                      "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                      !notification.isRead ? "font-semibold" : ""
                                    )}>
                                      {notification.fromUser.name}
                                    </h4>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {formatDate(notification.timestamp)}
                                    </span>
                                  </div>
                                  <p className={classNames(
                                    "text-xs line-clamp-2",
                                    !notification.isRead ? "font-medium text-gray-900 dark:text-gray-100" : "text-gray-500 dark:text-gray-400"
                                  )}>
                                    {notification.content}
                                  </p>
                                </div>
                                {!notification.isRead && (
                                  <span className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-2"></span>
                                )}
                              </div>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                    
                    <div className="px-4 py-3 border-t border-gray-100 dark:border-gray-700 text-center">
                      <button 
                        onClick={handleViewAllNotifications}
                        className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                      >
                        View all notifications
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Messages icon with notification badge and dropdown */}
            {user && (
              <div className="relative" ref={messagesRef}>
                <button
                  onClick={() => setMessagesOpen(!messagesOpen)}
                  className="mr-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-500 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 relative"
                >
                  <ChatBubbleLeftRightIcon className="w-6 h-6" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center pulse-animation">
                      {unreadCount}
                    </span>
                  )}
                </button>
                
                {messagesOpen && (
                  <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-50 dark:bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-custom-gray-800 rounded-xl shadow-lg z-50 animate-fade-in">
                    <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-700 flex justify-between items-center">
                      <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Messages</h3>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setMessagesOpen(false);
                          handleNewMessage();
                        }}
                        className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                        title="New message"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                        </svg>
                      </button>
                    </div>
                    
                    <div className="max-h-96 overflow-y-auto">
                      {recentConversations.length === 0 ? (
                        <div className="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                          No messages yet
                        </div>
                      ) : (
                        <ul className="divide-y divide-gray-100 dark:divide-gray-700">
                          {recentConversations.map(conversation => {
                            const otherParticipant = getOtherParticipant(conversation);
                            const lastMessage = conversation.lastMessage;
                            const hasUnread = conversation.unreadCount > 0;
                            
                            return (
                              <li 
                                key={conversation.id}
                                onClick={() => handleConversationClick(conversation.id)}
                                className={classNames(
                                  "p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors relative",
                                  hasUnread ? "bg-blue-50/30 dark:bg-blue-900/20" : ""
                                )}
                              >
                                <div className="flex items-start gap-2">
                                  <div 
                                    className={classNames(
                                      "w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 flex-shrink-0",
                                      hasUnread ? "pulse-animation" : ""
                                    )}
                                  >
                                    {otherParticipant?.name.charAt(0)}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex justify-between items-baseline">
                                      <h4 className={classNames(
                                        "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                        hasUnread ? "font-semibold" : ""
                                      )}>
                                        {otherParticipant?.name || 'Unknown'}
                                      </h4>
                                      {lastMessage && (
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                          {formatDate(lastMessage.timestamp)}
                                        </span>
                                      )}
                                    </div>
                                    {lastMessage && (
                                      <p className={classNames(
                                        "text-xs line-clamp-1",
                                        hasUnread ? "font-medium text-gray-900 dark:text-gray-100" : "text-gray-500 dark:text-gray-400"
                                      )}>
                                        {lastMessage.senderId === 'current-user' && 'You: '}
                                        {lastMessage.content}
                                      </p>
                                    )}
                                  </div>
                                  {hasUnread && (
                                    <span className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-2"></span>
                                  )}
                                </div>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                    
                    <div className="px-4 py-3 border-t border-gray-100 dark:border-gray-700 text-center">
                      <button 
                        onClick={handleViewAllMessages}
                        className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                      >
                        View all messages
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
            {user ? (
              <div className="relative" ref={menuRef}>
                <button
                  className="flex items-center text-sm text-gray-700 dark:text-gray-200 focus:outline-none rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 px-3 py-2 transition-colors"
                  onClick={() => setMenuOpen((open) => !open)}
                  aria-haspopup="true"
                  aria-expanded={menuOpen}
                >
                  <Avatar firstName={user.firstName} lastName={user.lastName} src={user.avatarUrl} size={32} className="mr-2" />
                  <span className="font-semibold hidden sm:inline-block">{userNameUtils.getFullName(user) || 'User'}</span>
                  <svg className="w-4 h-4 ml-1 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                </button>
                {menuOpen && (
                  <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-lg z-50 animate-fade-in">
                    {/* User Info Section */}
                    <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                      <div className="flex items-center space-x-3 mb-3">
                        <Avatar firstName={user.firstName} lastName={user.lastName} src={user?.avatarUrl} size={48} />
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">{userNameUtils.getFullName(user)}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{user?.email}</p>

                        </div>
                      </div>
                      

                    </div>

                    {/* Menu Items */}
                    <ul className="py-1">
                      <li>
                        <Link to={profileUtils.getOwnProfileUrl(user)} className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <UserIcon className="w-5 h-5 mr-3" />
                          Profile
                        </Link>
                      </li>
                      <li>
                        <Link to="/settings" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <Cog6ToothIcon className="w-5 h-5 mr-3" />
                          Settings
                        </Link>
                      </li>
                      <li>
                        <Link to="/activity" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <ClockIcon className="w-5 h-5 mr-3" />
                          Your activity
                        </Link>
                      </li>
                      <li>
                        <Link to="/saved-pins" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <BookmarkIcon className="w-5 h-5 mr-3" />
                          Saved
                        </Link>
                      </li>
                      <li>
                        <Link to="/settings#appearance" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <MoonIcon className="w-5 h-5 mr-3" />
                          Switch appearance
                        </Link>
                      </li>
                      <li>
                        <Link to="/feedback?tab=problem" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <ExclamationTriangleIcon className="w-5 h-5 mr-3" />
                          Report a problem
                        </Link>
                      </li>
                      <li>
                        <Link to="/feedback" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <QuestionMarkCircleIcon className="w-5 h-5 mr-3" />
                          Help & Feedback
                        </Link>
                      </li>
                      {/* Admin Area - Only visible to admins */}
                      {permissions.canAccessAdmin && (
                        <li>
                          <Link to="/admin" className="flex items-center px-4 py-2 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors">
                            <ShieldCheckIcon className="w-5 h-5 mr-3" />
                            Admin Area
                          </Link>
                        </li>
                      )}
                      <li className="border-t border-gray-100 dark:border-gray-700">
                        <button
                          onClick={async () => await onLogout?.()}
                          className="flex items-center w-full px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          <ArrowRightOnRectangleIcon className="w-5 h-5 mr-3" />
                          Logout
                        </button>
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="rounded-xl font-semibold text-sm px-4 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  Log in
                </Link>
                <Button
                  onClick={() => navigate('/signup')}
                  variant="primary"
                  size="sm"
                >
                  Sign up
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>


    </header>
  );
}; 