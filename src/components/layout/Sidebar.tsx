import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, MapIcon, Squares2X2Icon, HomeIcon, ShieldCheckIcon, ShoppingBagIcon, HeartIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';
import { Avatar } from '../ui/Avatar';
import type { User } from '@/types';
import { preloadOnHover } from '@/utils/preloadRoutes';
import { useAuthStore } from '@/store/authStore';
import { userNameUtils } from '@/utils/nameUtils';

export interface SidebarProps {
  open: boolean;
  onClose: () => void;
  user: User | null;
  onLogout: () => Promise<void>;
}

export const Sidebar = ({ open, onClose, user, onLogout }: SidebarProps) => {
  const { permissions } = useAuthStore();
  
  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-300"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500/95 dark:bg-gray-900/90 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 left-0 flex max-w-full pr-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-300"
                enterFrom="-translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-300"
                leaveFrom="translate-x-0"
                leaveTo="-translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md border-0 custom-sidebar-panel">
                  <div className="flex h-full flex-col overflow-y-auto scrollbar-theme bg-white dark:bg-gray-800 transition-colors duration-200 border-0 !border-r-0">
                    <div className="px-4 py-6 sm:px-6 border-b border-gray-100 dark:border-custom-gray-800">
                      <div className="flex items-start justify-end">
                        <div className="ml-3 flex h-7 items-center">
                          <button
                            type="button"
                            className="rounded-xl p-2 text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
                            onClick={onClose}
                          >
                            <span className="sr-only">Close panel</span>
                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="relative mt-2 flex-1 px-4 sm:px-6">
                      {user && (
                        <div className="flex items-center space-x-3 mb-6">
                          <Avatar firstName={user.firstName} lastName={user.lastName} src={user.avatarUrl} size={40} />
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{userNameUtils.getFullName(user)}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{user.email}</p>
                          </div>
                        </div>
                      )}
                      <nav className="space-y-1">
                        <Link
                          to="/"
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          onClick={onClose}
                          onMouseEnter={() => preloadOnHover('home')}
                        >
                          <HomeIcon className="h-5 w-5 mr-3 text-gray-400 dark:text-blue-400" />
                          Home
                        </Link>
                        <Link
                          to="/explore"
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          onClick={onClose}
                          onMouseEnter={() => preloadOnHover('explore')}
                        >
                          <svg 
                            aria-label="Explore" 
                            className="h-5 w-5 mr-3 text-gray-400 dark:text-blue-400" 
                            fill="currentColor" 
                            viewBox="0 0 24 24" 
                            role="img"
                          >
                            <title>Explore</title>
                            <polygon 
                              fill="none" 
                              points="13.941 13.953 7.581 16.424 10.06 10.056 16.42 7.585 13.941 13.953" 
                              stroke="currentColor" 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth="2"
                            />
                            <polygon 
                              fillRule="evenodd" 
                              points="10.06 10.056 13.949 13.945 7.581 16.424 10.06 10.056"
                            />
                            <circle 
                              cx="12.001" 
                              cy="12.005" 
                              fill="none" 
                              r="10.5" 
                              stroke="currentColor" 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth="2"
                            />
                          </svg>
                          Explorar
                        </Link>
                        <Link
                          to="/trading-map"
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          onClick={onClose}
                          onMouseEnter={() => preloadOnHover('tradingMap')}
                        >
                          <MapIcon className="h-5 w-5 mr-3 text-gray-400 dark:text-blue-400" />
                          Trading Map
                        </Link>
                        <Link
                          to="/my-pins"
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          onClick={onClose}
                        >
                          <Squares2X2Icon className="h-5 w-5 mr-3 text-gray-400 dark:text-blue-400" />
                          My Pins
                        </Link>
                        
                        <Link
                          to="/marketplace"
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          onClick={onClose}
                        >
                          <ShoppingBagIcon className="h-5 w-5 mr-3 text-gray-400 dark:text-blue-400" />
                          Marketplace
                        </Link>
                        
                        <button
                          className="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                          onClick={() => {
                            // TODO: Open wishlist modal
                            console.log('Open wishlist');
                            onClose();
                          }}
                        >
                          <HeartIcon className="h-5 w-5 mr-3 text-gray-400 dark:text-pink-400" />
                          My Wishlist
                        </button>
                        
                        {/* Admin Area - Only visible to admins */}
                        {permissions.canAccessAdmin && (
                          <Link
                            to="/admin"
                            className="flex items-center px-3 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
                            onClick={onClose}
                            onMouseEnter={() => preloadOnHover('admin')}
                          >
                            <ShieldCheckIcon className="h-5 w-5 mr-3 text-purple-500 dark:text-purple-400" />
                            Admin Area
                          </Link>
                        )}
                      </nav>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}; 