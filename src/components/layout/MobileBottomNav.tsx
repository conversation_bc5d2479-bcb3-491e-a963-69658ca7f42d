import { Link, useLocation } from 'react-router-dom';
import { 
  HomeIcon, 
  MapIcon,
  Squares2X2Icon,
  BookmarkIcon,
  BellIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';
import { 
  HomeIcon as HomeIconSolid,
  MapIcon as MapIconSolid,
  Squares2X2Icon as Squares2X2IconSolid,
  BookmarkIcon as BookmarkIconSolid,
  BellIcon as BellIconSolid,
  ShoppingBagIcon as ShoppingBagIconSolid
} from '@heroicons/react/24/solid';
import { Avatar } from '../ui/Avatar';
import { useNotifications } from '@/hooks/useNotifications';
import type { User } from '@/types';

interface MobileBottomNavProps {
  user: User | null;
}

export const MobileBottomNav = ({ user }: MobileBottomNavProps) => {
  const location = useLocation();
  const { unreadCount: notificationUnreadCount } = useNotifications(user?.id || '');

  const navigationItems = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      isActive: location.pathname === '/'
    },
    {
      name: 'Explore',
      href: '/explore',
      icon: (props: any) => (
        <svg {...props} fill="currentColor" viewBox="0 0 24 24">
          <polygon fill="none" points="13.941 13.953 7.581 16.424 10.06 10.056 16.42 7.585 13.941 13.953" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"/>
          <circle cx="12.001" cy="12.005" fill="none" r="10.5" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"/>
        </svg>
      ),
      iconSolid: (props: any) => (
        <svg {...props} fill="currentColor" viewBox="0 0 24 24">
          <path d="m13.173 13.164 1.491-3.829-3.83 1.49ZM12.001.5a11.5 11.5 0 1 0 11.5 11.5A11.513 11.513 0 0 0 12.001.5Zm5.35 7.443-2.478 6.369a1 1 0 0 1-.57.569l-6.36 2.47a1 1 0 0 1-1.294-1.294l2.48-6.369a1 1 0 0 1 .57-.569l6.359-2.47a1 1 0 0 1 1.294 1.294Z"/>
        </svg>
      ),
      isActive: location.pathname === '/explore'
    },
    {
      name: 'Marketplace',
      href: '/marketplace',
      icon: ShoppingBagIcon,
      iconSolid: ShoppingBagIconSolid,
      isActive: location.pathname === '/marketplace'
    },
    {
      name: 'My Pins',
      href: '/my-pins',
      icon: Squares2X2Icon,
      iconSolid: Squares2X2IconSolid,
      isActive: location.pathname === '/my-pins'
    },
    {
      name: 'Notifications',
      href: '/notifications',
      icon: BellIcon,
      iconSolid: BellIconSolid,
      isActive: location.pathname === '/notifications',
      badge: notificationUnreadCount > 0 ? notificationUnreadCount : undefined
    },
    {
      name: 'Profile',
      href: `/profile/${user?.username || user?.id}`,
      icon: null, // Será o avatar
      iconSolid: null,
      isActive: location.pathname.includes('/profile'),
      isProfile: true
    }
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 z-50">
      <div className="flex items-center justify-around h-16 px-2">
        {navigationItems.map((item) => {
          const IconComponent = item.isActive ? item.iconSolid : item.icon;
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex flex-col items-center justify-center flex-1 py-2 transition-colors relative ${
                item.isActive
                  ? 'text-blue-600 dark:text-blue-400'
                  : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              <div className="flex items-center justify-center w-6 h-6 mb-1 relative">
                {item.isProfile && user ? (
                  <Avatar 
                    firstName={user.firstName} 
                    lastName={user.lastName} 
                    src={user.avatarUrl} 
                    size={24}
                    className={item.isActive ? 'ring-2 ring-blue-500 dark:ring-blue-400' : ''}
                  />
                ) : IconComponent ? (
                  <IconComponent className="w-6 h-6" />
                ) : null}
                {item.badge && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium text-[10px]">
                    {item.badge > 9 ? '9+' : item.badge}
                  </span>
                )}
              </div>
              <span className="text-xs font-medium">{item.name}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}; 