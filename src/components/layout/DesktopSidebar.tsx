import { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  HomeIcon, 
  ChatBubbleLeftRightIcon,
  BellIcon,
  UserIcon,
  MapIcon,
  Squares2X2Icon,
  BookmarkIcon,
  ShoppingBagIcon,
  Bars3Icon,
  ShieldCheckIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  QuestionMarkCircleIcon,
  ClockIcon,
  MoonIcon,
  SunIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import { 
  HomeIcon as HomeIconSolid,
  ChatBubbleLeftRightIcon as MessagesIconSolid,
  BellIcon as BellIconSolid,
  UserIcon as UserIconSolid,
  MapIcon as MapIconSolid,
  Squares2X2Icon as Squares2X2IconSolid,
  BookmarkIcon as BookmarkIconSolid,
  ShoppingBagIcon as ShoppingBagIconSolid,
  PlusIcon as PlusIconSolid
} from '@heroicons/react/24/solid';
import { Avatar } from '../ui/Avatar';
import { useAuthStore } from '@/store/authStore';
import { usePermissions } from '@/hooks/usePermissions';
import { useMessageStore } from '@/store/messageStore';
import { useNotifications } from '@/hooks/useNotifications';
import { userNameUtils } from '@/utils/nameUtils';
import { AddPinModal } from '@/modules/pins/components/AddPinModal';
import { pinsService } from '@/services/pinsService';
import toast from 'react-hot-toast';
import { useTheme } from '@/theme/ThemeProvider';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import type { User } from '@/types';

// Componente de ícone personalizado para fechar sidebar
const CloseSidebarIcon = ({ className }: { className?: string }) => (
  <svg 
    width="20" 
    height="20" 
    viewBox="0 0 20 20" 
    fill="currentColor" 
    xmlns="http://www.w3.org/2000/svg" 
    className={className}
  >
    <path d="M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.167C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z"/>
  </svg>
);

interface DesktopSidebarProps {
  user: User | null;
  onLogout: () => Promise<void>;
}

export const DesktopSidebar = ({ user, onLogout }: DesktopSidebarProps) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [showThemeMenu, setShowThemeMenu] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { permissions } = useAuthStore();
  const { hasAdminAccess, canAccessAdmin } = usePermissions();
  const unreadCount = useMessageStore((state) => state.getUnreadCount());
  const { unreadCount: notificationUnreadCount } = useNotifications(user?.id || '');
  const moreMenuRef = useRef<HTMLDivElement>(null);
  const { theme, setTheme, actualTheme } = useTheme();

  // Fechar menu "More" quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (moreMenuRef.current && !moreMenuRef.current.contains(event.target as Node)) {
        setShowMoreMenu(false);
        setShowThemeMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const navigationItems = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      isActive: location.pathname === '/'
    },
    {
      name: 'Explore',
      href: '/explore',
      icon: (props: any) => (
        <svg {...props} fill="currentColor" viewBox="0 0 24 24">
          <polygon fill="none" points="13.941 13.953 7.581 16.424 10.06 10.056 16.42 7.585 13.941 13.953" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"/>
          <circle cx="12.001" cy="12.005" fill="none" r="10.5" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"/>
        </svg>
      ),
      iconSolid: (props: any) => (
        <svg {...props} fill="currentColor" viewBox="0 0 24 24">
          <path d="m13.173 13.164 1.491-3.829-3.83 1.49ZM12.001.5a11.5 11.5 0 1 0 11.5 11.5A11.513 11.513 0 0 0 12.001.5Zm5.35 7.443-2.478 6.369a1 1 0 0 1-.57.569l-6.36 2.47a1 1 0 0 1-1.294-1.294l2.48-6.369a1 1 0 0 1 .57-.569l6.359-2.47a1 1 0 0 1 1.294 1.294Z"/>
        </svg>
      ),
      isActive: location.pathname === '/explore'
    },
    {
      name: 'Create',
      href: '#',
      icon: PlusIcon,
      iconSolid: PlusIconSolid,
      isActive: false,
      isCreateButton: true,
      onClick: () => setShowCreateModal(true)
    },
    {
      name: 'My Pins',
      href: '/my-pins',
      icon: Squares2X2Icon,
      iconSolid: Squares2X2IconSolid,
      isActive: location.pathname === '/my-pins'
    },
    {
      name: 'Saved Pins',
      href: '/saved-pins',
      icon: BookmarkIcon,
      iconSolid: BookmarkIconSolid,
      isActive: location.pathname === '/saved-pins'
    },
    {
      name: 'Marketplace',
      href: '/marketplace',
      icon: ShoppingBagIcon,
      iconSolid: ShoppingBagIconSolid,
      isActive: location.pathname === '/marketplace'
    },

    {
      name: 'Messages',
      href: '/messages',
      icon: ChatBubbleLeftRightIcon,
      iconSolid: MessagesIconSolid,
      isActive: location.pathname.startsWith('/messages'),
      badge: unreadCount > 0 ? unreadCount : undefined
    },
    {
      name: 'Notifications',
      href: '/notifications',
      icon: BellIcon,
      iconSolid: BellIconSolid,
      isActive: location.pathname === '/notifications',
      badge: notificationUnreadCount > 0 ? notificationUnreadCount : undefined
    },
    {
      name: 'Trading Map',
      href: '/trading-map',
      icon: MapIcon,
      iconSolid: MapIconSolid,
      isActive: location.pathname === '/trading-map'
    },
    {
      name: 'Profile',
      href: `/profile/${user?.username || user?.id}`,
      icon: UserIcon,
      iconSolid: UserIconSolid,
      isActive: location.pathname.includes('/profile'),
      isProfile: true
    }
  ];

  const moreItems = [
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon
    },
    {
      name: 'Your activity',
      href: '/activity', // TODO: Implementar página de atividades
      icon: ClockIcon
    },
    {
      name: 'Saved',
      href: '/saved-pins',
      icon: BookmarkIcon
    },
    {
      name: 'Switch appearance',
      icon: actualTheme === 'dark' ? SunIcon : MoonIcon,
      onClick: () => {
        setShowThemeMenu(!showThemeMenu);
      },
      subtitle: theme === 'system' ? `System (${actualTheme})` : theme.charAt(0).toUpperCase() + theme.slice(1)
    },
    {
      name: 'Report a problem',
      href: '/feedback?tab=problem',
      icon: ExclamationTriangleIcon
    },
    {
      name: 'Help & Feedback',
      href: '/feedback',
      icon: QuestionMarkCircleIcon
    },
    ...(hasAdminAccess || canAccessAdmin || permissions.canAccessAdmin || user?.role === 'admin' ? [{
      name: 'Admin Area',
      href: '/admin',
      icon: ShieldCheckIcon
    }] : [])
  ];

  // Debug temporário das permissões (reduced logging to prevent spam)
  if (process.env.NODE_ENV === 'development' && Math.random() < 0.05) { // Log only 5% of the time
    console.log('🔍 Sidebar Permissions Debug:', {
      hasAdminAccess,
      canAccessAdmin,
      'user.role': user?.role,
      'final condition': hasAdminAccess || canAccessAdmin || permissions.canAccessAdmin || user?.role === 'admin'
    });
  }

  const handleLogout = async () => {
    await onLogout();
    setShowMoreMenu(false);
  };

  const handleCreatePin = async (pinData: {
    title: string;
    description?: string;
    imageUrl: string;
    origin?: string;
    releaseYear?: number;
    originalPrice?: number;
    pinNumber?: string;
    availability?: 'display' | 'trade' | 'sale';
    salePrice?: number;
    condition?: 'new' | 'like-new' | 'good' | 'fair';
    boardId?: string;
  }) => {
    if (!user) {
      toast.error('You must be logged in to create a pin');
      return;
    }

    try {
      const createdPin = await pinsService.create({
        ...pinData,
        userId: user.id
      });
      
      // If a boardId is provided, add the pin to that board
      if (pinData.boardId && createdPin?.id) {
        try {
          await pinsService.addPinToBoard(createdPin.id, pinData.boardId);
          toast.success('Pin created and added to board successfully!');
        } catch (boardError) {
          console.error('Error adding pin to board:', boardError);
          toast.success('Pin created successfully, but could not add to board');
        }
      } else {
        toast.success('Pin created successfully!');
      }
      
      setShowCreateModal(false);
      
      // Navigate to My Pins page to see the new pin
      navigate('/my-pins');
    } catch (error: any) {
      console.error('Error creating pin:', error);
      toast.error(error.message || 'Failed to create pin');
    }
  };

  return (
    <div className={`sidebar-container fixed left-0 top-0 h-full bg-white dark:bg-black border-r border-gray-200 dark:border-gray-800 transition-all duration-300 z-40 ${
      isExpanded ? 'w-64' : 'w-20'
    }`}>
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className={`p-6 flex items-center ${isExpanded ? 'justify-between' : 'justify-center'}`}>
          <Link to="/" className="flex items-center">
            {isExpanded ? (
              <img src="/images/system/pinpal-logo.png" alt="PinPal" className="h-20 w-auto" />
            ) : (
              <img 
                src="/images/system/pinpal-logo.png" 
                alt="PinPal" 
                className="sidebar-logo-collapsed h-7 w-auto object-contain" 
                onError={(e) => console.error('Logo failed to load:', e)}
                onLoad={() => console.log('Logo loaded successfully')}
              />
            )}
          </Link>
          {isExpanded && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-900"
            >
              <CloseSidebarIcon className="sidebar-icon w-6 h-6" />
            </button>
          )}
        </div>

        {/* Collapse Button - when collapsed */}
        {!isExpanded && (
          <div className="px-4 pb-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-full flex justify-center p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-900"
            >
              <CloseSidebarIcon className="sidebar-icon w-6 h-6 rotate-180" />
            </button>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-1">
          {navigationItems.map((item) => {
            const IconComponent = item.isActive ? item.iconSolid : item.icon;
            
            // Handle Create button differently (as button instead of link)
            if (item.isCreateButton) {
              return (
                <button
                  key={item.name}
                  onClick={item.onClick}
                  className={`flex items-center w-full px-4 py-4 rounded-xl transition-colors relative group ${
                    item.isActive 
                      ? 'bg-blue-100 dark:bg-gray-800 text-blue-900 dark:text-white font-medium' 
                      : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-900'
                  }`}
                >
                  <div className="relative flex items-center">
                    <IconComponent className="sidebar-icon w-7 h-7" />
                    {item.badge && (
                      <span className="absolute -top-2 -right-2 bg-red-500 text-white dark:text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                        {item.badge > 99 ? '99+' : item.badge}
                      </span>
                    )}
                  </div>
                  {isExpanded && (
                    <span className="ml-5 text-lg font-medium">{item.name}</span>
                  )}
                  {!isExpanded && (
                    <div className="absolute left-full ml-8 px-3 py-2 bg-gray-800 dark:bg-gray-800 text-white dark:text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 shadow-lg">
                      {item.name}
                    </div>
                  )}
                </button>
              );
            }
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-4 py-4 rounded-xl transition-colors relative group ${
                  item.isActive 
                    ? 'bg-blue-100 dark:bg-gray-800 text-blue-900 dark:text-white font-medium' 
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
              >
                <div className="relative flex items-center">
                  {item.isProfile && user ? (
                    <Avatar
                      firstName={user.firstName}
                      lastName={user.lastName}
                      src={user.avatarUrl}
                      size={28}
                      className={`sidebar-avatar ${item.isActive ? 'ring-2 ring-white' : ''}`}
                    />
                  ) : (
                    <IconComponent className="sidebar-icon w-7 h-7" />
                  )}
                  {item.badge && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white dark:text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                {isExpanded && (
                  <span className="ml-5 text-lg font-medium">{item.name}</span>
                )}
                {!isExpanded && (
                  <div className="absolute left-full ml-8 px-3 py-2 bg-gray-800 dark:bg-gray-800 text-white dark:text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 shadow-lg">
                    {item.name}
                  </div>
                )}
              </Link>
            );
          })}
        </nav>

        {/* More Menu */}
        <div className="px-4 pb-6 relative" ref={moreMenuRef}>
          <button
            onClick={() => setShowMoreMenu(!showMoreMenu)}
            className={`more-menu-button flex items-center w-full px-4 py-4 rounded-xl transition-colors group ${
              showMoreMenu 
                ? 'bg-blue-100 dark:bg-gray-800 text-blue-900 dark:text-white' 
                : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-900'
            }`}
          >
            <Bars3Icon className="sidebar-icon w-7 h-7" />
            {isExpanded && <span className="ml-5 text-lg font-medium">More</span>}
            {!isExpanded && (
              <div className="absolute left-full ml-8 px-3 py-2 bg-gray-800 text-white dark:text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 shadow-lg">
                More
              </div>
            )}
          </button>

          {/* More Dropdown */}
          {showMoreMenu && (
            <div className={`absolute bottom-full mb-1 border border-gray-300 dark:border-gray-600 rounded-xl shadow-2xl p-2 z-50 backdrop-blur-sm bg-white dark:bg-gray-900 ${
              isExpanded ? 'left-4 w-72' : 'left-full ml-8 w-52'
            }`}>
              {moreItems.map((item) => {
                if (item.onClick) {
                  return (
                    <button
                      key={item.name}
                      onClick={() => {
                        item.onClick();
                        setShowMoreMenu(false);
                      }}
                      className="flex items-center w-full px-4 py-3 text-gray-700 dark:text-white hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 rounded-lg"
                    >
                      <item.icon className="w-6 h-6 mr-4" />
                      <span className="font-medium flex-1">{item.name}</span>
                      {item.subtitle && (
                        <span className="text-sm text-gray-500">{item.subtitle}</span>
                      )}
                    </button>
                  );
                }
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => setShowMoreMenu(false)}
                    className="flex items-center px-4 py-3 text-gray-700 dark:text-white hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 rounded-lg"
                  >
                    <item.icon className="w-6 h-6 mr-4" />
                    <span className="font-medium">{item.name}</span>
                  </Link>
                );
              })}
              <hr className="my-3 border-gray-300 dark:border-gray-600 mx-6" />
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-4 py-3 text-gray-700 dark:text-white hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 rounded-lg"
              >
                <ArrowRightOnRectangleIcon className="w-6 h-6 mr-4" />
                <span className="font-medium">Logout</span>
              </button>
            </div>
          )}
          
          {/* Theme Submenu */}
          {showThemeMenu && (
            <div className={`absolute bottom-full mb-1 border border-gray-300 dark:border-gray-600 rounded-xl shadow-2xl p-2 z-50 backdrop-blur-sm bg-white dark:bg-gray-900 ${
              isExpanded ? 'left-4 w-64' : 'left-full ml-8 w-48'
            }`}>
              <div className="mb-2 px-4 py-2">
                <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400">Choose theme</h4>
              </div>
              <button
                onClick={() => {
                  setTheme('light');
                  setShowThemeMenu(false);
                  setShowMoreMenu(false);
                }}
                className={`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 ${
                  theme === 'light'
                    ? 'bg-blue-100 dark:bg-gray-800 text-blue-900 dark:text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <SunIcon className="w-6 h-6 mr-4" />
                <span className="font-medium">Light</span>
                {theme === 'light' && (
                  <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full" />
                )}
              </button>
              <button
                onClick={() => {
                  setTheme('dark');
                  setShowThemeMenu(false);
                  setShowMoreMenu(false);
                }}
                className={`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 ${
                  theme === 'dark'
                    ? 'bg-blue-100 dark:bg-gray-800 text-blue-900 dark:text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <MoonIcon className="w-6 h-6 mr-4" />
                <span className="font-medium">Dark</span>
                {theme === 'dark' && (
                  <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full" />
                )}
              </button>
              <button
                onClick={() => {
                  setTheme('system');
                  setShowThemeMenu(false);
                  setShowMoreMenu(false);
                }}
                className={`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 ${
                  theme === 'system'
                    ? 'bg-blue-100 dark:bg-gray-800 text-blue-900 dark:text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <ComputerDesktopIcon className="w-6 h-6 mr-4" />
                <span className="font-medium">System</span>
                {theme === 'system' && (
                  <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full" />
                )}
              </button>
            </div>
          )}
        </div>

      </div>
      
      {/* Create Pin Modal */}
      {showCreateModal && (
        <AddPinModal
          onClose={() => setShowCreateModal(false)}
          onAddPin={handleCreatePin}
        />
      )}
    </div>
  );
};