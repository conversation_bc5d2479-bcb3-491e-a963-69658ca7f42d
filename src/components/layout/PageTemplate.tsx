import React, { ReactNode } from 'react';
import { PageHeader } from '../ui/PageHeader';

interface PageTemplateProps {
  /** T<PERSON><PERSON><PERSON> da página */
  title: ReactNode;
  /** URL para navegação ao clicar no botão voltar */
  backUrl?: string;
  /** Se verdadeiro, oculta o botão de voltar */
  hideBackButton?: boolean;
  /** Função personalizada para o clique do botão voltar */
  onBackClick?: () => void;
  /** Conteúdo a ser renderizado na área de ações do header */
  action?: ReactNode;
  /** Conteúdo principal da página */
  children: ReactNode;
  /** Classes CSS adicionais para o container principal */
  className?: string;
  /** Se verdadeiro, remove o padding padrão do conteúdo */
  noPadding?: boolean;
  /** Se verdadeiro, permite que o conteúdo ocupe toda a altura da tela */
  fullHeight?: boolean;
}

/**
 * Template padrão para páginas do sistema.
 * Inclui automaticamente:
 * - Seta de voltar (alinhada corretamente)
 * - Títu<PERSON> da página (usando sistema centralizado de títulos)
 * - Área de conteúdo com padding consistente
 * - Alinhamento correto dos elementos superiores
 */
export const PageTemplate: React.FC<PageTemplateProps> = ({
  title,
  backUrl,
  hideBackButton = false,
  onBackClick,
  action,
  children,
  className = '',
  noPadding = false,
  fullHeight = false,
}) => {
  const containerClasses = [
    fullHeight ? 'h-screen flex flex-col' : 'min-h-screen',
    'bg-white dark:bg-black',
    className
  ].filter(Boolean).join(' ');

  const contentClasses = [
    fullHeight ? 'flex-1 overflow-hidden' : '',
    noPadding ? '' : 'page-template-content px-4 pt-8 pb-8',
    // Aplicar largura máxima apenas quando não for fullHeight
    !fullHeight ? 'max-w-page mx-auto' : '',
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* Header com seta de voltar e título - alinhado ao topo */}
      <div className="sticky top-0 z-50 bg-white dark:bg-black">
        <PageHeader
          title={title}
          backUrl={backUrl}
          hideBackButton={hideBackButton}
          onBackClick={onBackClick}
          action={action}
        />
      </div>

      {/* Conteúdo principal */}
      <main className={contentClasses}>
        {children}
      </main>
    </div>
  );
};

// Componentes especializados para casos específicos

/**
 * Template para páginas de configurações e formulários
 */
export const SettingsPageTemplate: React.FC<Omit<PageTemplateProps, 'fullHeight'>> = (props) => (
  <PageTemplate {...props} fullHeight={true} />
);

/**
 * Template para páginas de listagem e exploração
 */
export const ListPageTemplate: React.FC<Omit<PageTemplateProps, 'noPadding'>> = (props) => (
  <PageTemplate {...props} noPadding={false} />
);

/**
 * Template para páginas com mapas ou conteúdo full-screen
 */
export const FullScreenPageTemplate: React.FC<Omit<PageTemplateProps, 'noPadding' | 'fullHeight'>> = (props) => (
  <PageTemplate {...props} noPadding={true} fullHeight={true} className="no-max-width" />
);

/**
 * Template para páginas de dashboard e home
 */
export const DashboardPageTemplate: React.FC<Omit<PageTemplateProps, 'hideBackButton'>> = (props) => (
  <PageTemplate {...props} hideBackButton={true} />
);

export default PageTemplate; 