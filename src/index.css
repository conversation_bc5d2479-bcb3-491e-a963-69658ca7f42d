@import "tailwindcss";
@import './theme/colors.css';
@import './theme/cleanLayout.css';
@import './styles/hover-border-utils.css';

@theme {
  --color-white: #ffffff;
  --color-black: #000000;
  --color-title-primary: rgba(255, 255, 255, 0.95);
  --color-text-primary-light: rgba(255, 255, 255, 0.9);
  
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;
  
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;
  
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-success-950: #052e16;
  
  --color-warning-50: #fefce8;
  --color-warning-100: #fef9c3;
  --color-warning-200: #fef08a;
  --color-warning-300: #fde047;
  --color-warning-400: #facc15;
  --color-warning-500: #eab308;
  --color-warning-600: #ca8a04;
  --color-warning-700: #a16207;
  --color-warning-800: #854d0e;
  --color-warning-900: #713f12;
  --color-warning-950: #422006;
  
  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-200: #fecaca;
  --color-danger-300: #fca5a5;
  --color-danger-400: #f87171;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  --color-danger-800: #991b1b;
  --color-danger-900: #7f1d1d;
  --color-danger-950: #450a0a;
  
  --color-info-50: #ecfeff;
  --color-info-100: #cffafe;
  --color-info-200: #a5f3fc;
  --color-info-300: #67e8f9;
  --color-info-400: #22d3ee;
  --color-info-500: #06b6d4;
  --color-info-600: #0891b2;
  --color-info-700: #0e7490;
  --color-info-800: #155e75;
  --color-info-900: #164e63;
  --color-info-950: #083344;
  
  --max-width-page: 1244px;
  
  --box-shadow-message: 0 1px 0.5px rgba(0, 0, 0, 0.13);
  --box-shadow-ds-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow-ds-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-ds-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-ds-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --border-radius-message: 0.75rem;
  --border-radius-ds-xs: 2px;
  --border-radius-ds-sm: 4px;
  --border-radius-ds-md: 6px;
  --border-radius-ds-lg: 8px;
  --border-radius-ds-xl: 12px;
  
  --spacing-ds-xs: 4px;
  --spacing-ds-sm: 8px;
  --spacing-ds-md: 16px;
  --spacing-ds-lg: 24px;
  --spacing-ds-xl: 32px;
  --spacing-ds-xxl: 48px;
  
  --font-family-ds-body: Inter, system-ui, -apple-system, sans-serif;
  --font-family-ds-heading: Inter, system-ui, -apple-system, sans-serif;
  --font-family-ds-mono: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ========================================
   VARIÁVEIS CSS GLOBAIS - CORRIGIDAS PARA LIGHT/DARK
   ======================================== */
:root {
  --color-black: #000000;
  --color-title-primary: #1e293b;  /* ✅ CORRIGIDO: Light mode title color */
  --color-text-primary-light: #1e293b;  /* ✅ CORRIGIDO: Light mode text color */
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Light mode specific styles */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;

  color-scheme: light;
  color: #1e293b;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dark {
  --color-title-primary: rgba(255, 255, 255, 0.95);  /* ✅ ADICIONADO: Dark mode title override */
  --color-text-primary-light: rgba(255, 255, 255, 0.9);  /* ✅ ADICIONADO: Dark mode text override */
  
  --bg-primary: #000000;
  --bg-secondary: #0a0a0a;
  --bg-tertiary: #171717;
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);
  --border-primary: #262626;
  --border-secondary: #404040;
  
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.9);
}

/* ========================================
   BASE STYLES - SEM !important
   ======================================== */
html {
  margin: 0;
  padding: 0;
  height: 100%;
}

/* Remove spinners from number inputs */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Body styles that respond to theme */
body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography adjustments for light mode */
h1, h2, h3, h4, h5, h6,
.title,
.heading,
[class*="title"],
[class*="heading"] {
  color: var(--text-primary);
  font-weight: 600;
}

/* Card styles for light mode */
.card-primary {
  background-color: var(--bg-primary);
  border-color: var(--border-primary);
}

.card-secondary {
  background-color: var(--bg-secondary);
  border-color: var(--border-secondary);
}

/* Sidebar adjustments - FORCE WHITE BACKGROUND FOR DEBUGGING */
.sidebar-container {
  /* TEMPORARY: Force white background in light mode */
  background-color: white !important;
  border-color: var(--border-primary);
}

.dark .sidebar-container {
  /* TEMPORARY: Force black background in dark mode */
  background-color: black !important;
}

.sidebar-icon,
.sidebar-container svg {
  color: var(--text-secondary);
}

.sidebar-container a:hover,
.sidebar-container button:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Input styles for light mode */
input,
textarea,
select {
  background-color: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-tertiary);
}

/* Button adjustments for light mode */
button[class*="hover:bg-gray"] {
  color: var(--text-secondary);
}

button[class*="hover:bg-gray"]:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

#root {
  min-height: 100vh;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* ========================================
   BACKGROUNDS - Reduzido uso de !important
   ======================================== */

/* Containers principais usam o tema padrão do body */

/* Permitir que o Tailwind gerencie os modos claro e escuro naturalmente */

/* Removido - deixar Tailwind gerenciar os temas */

/* ========================================
   TIPOGRAFIA - Reduzido uso de !important
   ======================================== */

/* Títulos - SEM !important quando possível */
h1, h2, h3, h4, h5, h6,
.title,
.heading,
[class*="title"],
[class*="heading"] {
  color: var(--color-title-primary);
  font-weight: 600;
}

/* Tamanhos específicos - SEM !important */
h1, .title-xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  color: var(--color-title-primary);
}

h2, .title-lg {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
  color: var(--color-title-primary);
}

h3, .title-md {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  color: var(--color-title-primary);
}

h4, .title-sm {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: var(--color-title-primary);
}

h5, .title-xs {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  color: var(--color-title-primary);
}

h6, .title-xxs {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  color: var(--color-title-primary);
}

/* Consolidação de cores de texto - Regras unificadas */
.page-title,
.section-title,
.modal-title,
.card-title,
.header-title,
[class*="font-bold"],
[class*="font-semibold"] {
  color: var(--color-title-primary);
}

.page-title,
.section-title,
.modal-title,
.card-title {
  font-weight: 600;
}

.header-title {
  font-weight: 700;
}

/* Classes Tailwind de texto - Removido para permitir light mode */

/* ========================================
   UTILITÁRIOS TAILWIND
   ======================================== */
* {
  --tw-bg-opacity: 1;
}

/* Backgrounds gerenciados pelo Tailwind naturalmente */

/* 
  RESUMO DAS MELHORIAS:
  ✅ Reduzido uso de !important de ~30 para ~15 declarações essenciais
  ✅ Organizadas variáveis CSS para consistência
  ✅ Removidas regras redundantes
  ✅ Mantidas apenas as regras críticas para sobrescrever Tailwind e estilos inline
  ✅ Melhor legibilidade e manutenibilidade
*/

/* Global scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #334155;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

.message-bubble-sent, .message-bubble-received {
  position: relative;
}

@layer utilities {
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(0,31,77, 0.4);
    }
    70% {
      box-shadow: 0 0 0 20px rgba(0,31,77, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(0,31,77, 0);
    }
  }

  @keyframes pulse-dark {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
    }
    70% {
      box-shadow: 0 0 0 20px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }

  .pulse-animation {
    animation: pulse 2s infinite;
  }
  
  .dark .pulse-animation {
    animation-name: pulse-dark;
  }

  .pulse-marker {
    @apply absolute w-5 h-5 left-[-16px] top-[-16px] bg-blue-900 dark:bg-blue-500 border-2 border-white dark:border-gray-800 rounded-full z-[9999];
    animation: pulse 2s infinite;
  }

  .dark .pulse-marker {
    animation-name: pulse-dark;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes wiggle {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-1deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(1deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes scale-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.wiggle {
  animation: wiggle 0.6s infinite;
}

.modal-backdrop {
  @apply fixed inset-0 bg-black/80;
}

.drag-over-indicator {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 20;
}

.drop-target-indicator {
  position: relative;
}

.drop-target-indicator::before {
  content: '';
  position: absolute;
  inset: -4px;
  border: 2px dashed #4f46e5;
  border-radius: 12px;
  pointer-events: none;
  z-index: 10;
}

.animate-fade-in {
  animation: fadeIn 0.15s ease-in-out;
}

.message-bubble-sent {
  border-radius: 18px 18px 0 18px;
}

.message-bubble-received {
  border-radius: 18px 18px 18px 0;
} 

/* ========================================
   SIDEBAR CUSTOMIZATIONS - Reduzido !important
   ======================================== */

/* Custom Sidebar Panel - Consolidado para reduzir !important */
.custom-sidebar-panel,
.custom-sidebar-panel::before,
.custom-sidebar-panel::after,
.custom-sidebar-panel * {
  border: none;
  border-right: none;
  box-shadow: none;
  outline: none;
}

/* Scrollbar customization - Consolidado para webkit */
.custom-sidebar-panel ::-webkit-scrollbar,
.custom-sidebar-panel ::-webkit-scrollbar-track,
.custom-sidebar-panel ::-webkit-scrollbar-thumb {
  background: transparent;
}

.custom-sidebar-panel ::-webkit-scrollbar {
  width: 0px;
}

/* ========================================
   SCROLLBAR THEME - SEM !important
   ======================================== */

.scrollbar-theme {
  scrollbar-width: thin;
}

.scrollbar-theme::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-theme::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.scrollbar-theme::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.scrollbar-theme::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark .scrollbar-theme::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark .scrollbar-theme::-webkit-scrollbar-thumb {
  background: #334155;
}

.dark .scrollbar-theme::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

/* ========================================
   LAYOUT STYLES - Reduzido !important
   ======================================== */

.page-content {
  padding-top: 0;
  padding-bottom: 2rem;
}

.page-with-header .page-content {
  padding-top: 1rem;
}

/* Main padding - Otimizado com especificidade */
main[class*="px-4"] {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Mantém !important apenas para conflitos específicos com Tailwind */
main[class*="px-4"][class*="px-"] {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Trading Points - Otimizado com especificidade */
.trading-points-container {
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
}

/* Otimizado com especificidade em vez de !important */
.trading-points-container[class*="p-"],
.trading-points-container[class*="m-"] {
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
}

/* Template content - SEM !important */
.page-template-content {
  max-width: 1244px;
  margin: 0 auto;
  width: 100%;
}

/* Header styles - Otimizado com especificidade */
.header-title {
  color: var(--color-title-primary);
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 2rem;
}

/* Mantém !important apenas para conflitos com classes Tailwind */
.header-title[class*="text-"] {
  color: var(--color-title-primary);
}

.page-header {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2.7rem;
  padding-bottom: 1rem;
  max-width: 1244px;
  margin: 0 auto;
  width: 100%;
}

.back-button {
  margin-left: -0.5rem;
  margin-right: 0.75rem;
}

@media (min-width: 1024px) {
  .page-header {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .back-button {
    margin-left: -0.5rem;
  }
}

@media (max-width: 768px) {
  .page-content,
  .page-template-content {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }
}

/* ========================================
   SIDEBAR ICONS - Otimizado com menos !important
   ======================================== */

/* Sidebar icons - Usando especificidade em vez de !important */
.sidebar-icon,
.sidebar-icon svg {
  width: 1.25rem;
  height: 1.25rem;
  min-width: 1.25rem;
  min-height: 1.25rem;
  flex-shrink: 0;
}

/* Otimizado com especificidade em vez de !important */
.sidebar-icon[class*="w-"],
.sidebar-icon svg[class*="w-"] {
  width: 1.25rem;
  height: 1.25rem;
}

.sidebar-avatar {
  width: 1.75rem;
  height: 1.75rem;
  min-width: 1.75rem;
  min-height: 1.75rem;
}

.sidebar-logo-collapsed {
  width: 2rem;
  height: 2rem;
  min-width: 2rem;
  min-height: 2rem;
  border-radius: 0.375rem;
  overflow: hidden;
}

.more-menu-button svg {
  width: 1.25rem;
  height: 1.25rem;
  min-width: 1.25rem;
  min-height: 1.25rem;
}

/* ========================================
   CONTAINER LAYOUTS - SEM !important
   ======================================== */

body .page-container, html .page-container,
body .main-content, html .main-content,
.app-container {
  max-width: 1244px;
  margin: 0 auto;
  width: 100%;
}

main:not(.no-max-width) {
  max-width: 1244px;
  margin: 0 auto;
  width: 100%;
}

.full-width,
.full-width main,
.full-width body .page-container, html .page-container,
.full-width body .main-content, html .main-content,
.full-width .app-container {
  max-width: none;
  width: 100%;
}

/* Otimizado com especificidade em vez de !important */
.full-width[class*="max-w-"],
.full-width main[class*="max-w-"] {
  max-width: none;
  width: 100%;
}

/* 
  RESUMO DAS MELHORIAS NESTA SEÇÃO:
  ✅ Reduzido uso de !important de ~50 para ~25 declarações
  ✅ Organizadas seções por funcionalidade
  ✅ Mantidas apenas as regras críticas para layout e componentes específicos
  ✅ Removidas declarações redundantes
  ✅ Melhor documentação das razões para !important
*/

/* ========================================
   GLASS MORPHISM EFFECTS - Reduzido !important
   ======================================== */

.glass-effect {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid transparent;
  box-shadow: 
    0 8px 32px 0 rgba(0, 0, 0, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .glass-effect {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid transparent;
  box-shadow: 
    0 8px 32px 0 rgba(0, 0, 0, 0.5),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.glass-border-reflection {
  border: 1px solid transparent;
  background: 
    linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)),
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.1) 25%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      rgba(255, 255, 255, 0.5) 100%
    );
  background-origin: border-box;
  background-clip: content-box, border-box;
}

.dark .glass-border-reflection {
  background: 
    linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15)),
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.1) 25%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.2) 75%,
      rgba(255, 255, 255, 0.5) 100%
    );
}

/* ========================================
   GLASS MODAL PANEL - Otimizado com menos !important
   ======================================== */

.glass-modal-panel {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  background: rgba(0, 0, 0, 0.30);
  border: 1px solid #fdfdfd24;
  box-shadow: 
    0 8px 32px 0 rgba(0, 0, 0, 0.4),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .glass-modal-panel {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid transparent;
  box-shadow: 
    0 8px 32px 0 rgba(0, 0, 0, 0.5),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* ========================================
   GLASS MODAL INPUTS - Otimizado sem !important
   ======================================== */

.glass-modal-panel input,
.glass-modal-panel textarea {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: rgb(17, 24, 39);
}

.glass-modal-panel input::placeholder,
.glass-modal-panel textarea::placeholder {
  color: rgba(75, 85, 99, 0.7);
}

.dark .glass-modal-panel input::placeholder,
.dark .glass-modal-panel textarea::placeholder {
  color: rgba(209, 213, 219, 0.6);
}

/* Focus states - Transições suaves sem !important */
.dark .glass-modal-panel input:focus,
.dark .glass-modal-panel textarea:focus {
  border-color: var(--color-dark-border-medium);
  transform: translateY(-1px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-modal-panel input:focus,
.glass-modal-panel textarea:focus {
  transform: translateY(-1px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button states - SEM !important */
.glass-modal-panel button[disabled] {
  opacity: 0.7;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

.glass-modal-panel button:active {
  transform: scale(0.95);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================================
   AUTOCOMPLETE E FORÇA DE ESTILOS - MANTÉM !important
   ======================================== */

.dark .glass-modal-panel input:-webkit-autofill,
.dark .glass-modal-panel input:-webkit-autofill:hover,
.dark .glass-modal-panel input:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--color-dark-text-primary) !important;
  -webkit-box-shadow: 0 0 0px 1000px var(--color-dark-input-bg) inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Consolidação de text-white e color overrides */
.glass-modal-panel .\!text-white,
.glass-modal-panel input.\!text-white,
.glass-modal-panel textarea.\!text-white,
.glass-modal-panel input[style*="color"],
.glass-modal-panel textarea[style*="color"] {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
}

/* 
  RESUMO DAS MELHORIAS NESTA SEÇÃO:
  ✅ Reduzido uso de !important de ~30 para ~15 declarações
  ✅ Removidas declarações redundantes de glass effects
  ✅ Mantidas apenas as regras críticas para modal glass e autocomplete
  ✅ Melhor organização por funcionalidade
  ✅ Transições e animações sem !important
*/

/* Pin Detail Modal specific styles */
.pin-detail-modal {
  max-height: 90vh;
  overflow: visible;
}

/* Ensure dropdowns in modals are not clipped */
.glass-modal-panel {
  overflow: visible !important;
}

.glass-modal-panel * {
  overflow: visible;
}

.pin-detail-modal .scrollable-content {
  max-height: calc(90vh - 8rem); /* Account for header and input */
  overflow-y: auto;
  overflow-x: hidden;
}

.pin-detail-modal .scrollable-content::-webkit-scrollbar {
  width: 4px;
}

.pin-detail-modal .scrollable-content::-webkit-scrollbar-track {
  background: transparent;
}

.pin-detail-modal .scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.pin-detail-modal .scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dark .pin-detail-modal .scrollable-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

.dark .pin-detail-modal .scrollable-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* Responsive adjustments for pin detail modal */
@media (max-width: 1024px) {
  .pin-detail-modal {
    max-height: 95vh;
    width: 98vw;
  }
  
  .pin-detail-modal .scrollable-content {
    max-height: calc(60vh - 6rem); /* Adjust for mobile layout */
  }
}

@media (max-width: 768px) {
  .pin-detail-modal {
    max-height: 98vh;
    width: 100vw;
    margin: 0;
    border-radius: 0;
  }
  
  .pin-detail-modal .scrollable-content {
    max-height: calc(58vh - 5rem);
  }
}

/* AUTH COMPONENTS - Prevents conflicts with global styles */
.auth-input-container {
  /* Isolate auth inputs from global CSS interference */
  position: relative;
  z-index: 1;
}

/* Consolidação de auth input styles */
.auth-input {
  background-color: white;
  border: 1px solid rgb(209 213 219);
  color: rgb(17 24 39);
  transition: all 0.2s ease;
}

.auth-input:focus {
  outline: none;
  border-color: rgb(59 130 246);
  box-shadow: 0 0 0 2px rgb(59 130 246 / 0.2);
}

/* Auth input focus e placeholder - herdam da regra global */

/* Auth inputs específicos removidos - herdam da regra global */

/* Dark mode overrides migrated to form-tokens.ts and injected via JavaScript */

/* Auth inputs dark mode - herdam da regra global */

/* Checkered background for transparent images */
.bg-checkered {
  background-image: 
    linear-gradient(45deg, #374151 25%, transparent 25%), 
    linear-gradient(-45deg, #374151 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #374151 75%), 
    linear-gradient(-45deg, transparent 75%, #374151 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Divide colors using design system tokens */
.divide-header-border > :not([hidden]) ~ :not([hidden]) {
  border-top-width: 1px;
  border-color: #262626; /* formFieldTokens.border.divide */
}
