import React, { useState } from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { GlassModal } from '../components/ui/GlassModal';
import { Modal } from '../components/ui/Modal';
import { Button } from '../components/ui/design-system/Button';
import { Input } from '../components/ui/design-system/Input';
import { StandardInput } from '../components/ui/forms/StandardInput';
import { StandardTextarea } from '../components/ui/forms/StandardTextarea';
import { 
  XMarkIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  PlusIcon,
  PhotoIcon,
  TrashIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

const meta = {
  title: 'Design System/Components/Modal',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# Modal System

Sistema completo de modais com suporte a glass morphism, diferentes tamanhos e casos de uso variados.

## Componentes Disponíveis

### GlassModal
Modal com efeito glassmorphism para interfaces modernas e elegantes.

### Modal (Base)
Modal tradicional para casos de uso gerais.

## Características

- **Glass Morphism**: Efeito de vidro com backdrop blur
- **Responsive**: Adaptação automática para diferentes telas
- **Keyboard Support**: Navegação por teclado (ESC para fechar)
- **Click Outside**: Fecha ao clicar fora do modal
- **Focus Management**: Foco automático no modal
- **Animations**: Transições suaves de entrada e saída

## Tamanhos

- **xs**: Extra small - Para avisos mínimos
- **sm**: Small - Para confirmações simples
- **md**: Medium - Tamanho padrão
- **lg**: Large - Para formulários
- **xl**: Extra Large - Para conteúdo extenso
- **2xl**: 2X Large - Para editores
- **full**: Tela cheia - Para visualizadores

## Casos de Uso

- Formulários de criação/edição
- Confirmações de ação
- Visualizadores de conteúdo
- Alertas e notificações
- Wizards multi-step
        `,
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

// ===== MODAL EXAMPLES =====

const ConfirmationModal = ({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  confirmText = "Confirm", 
  cancelText = "Cancel",
  variant = "danger"
}: {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "danger" | "success" | "info";
}) => {
  const icons = {
    danger: ExclamationTriangleIcon,
    success: CheckCircleIcon,
    info: InformationCircleIcon
  };

  const colors = {
    danger: "text-red-400",
    success: "text-green-400", 
    info: "text-blue-400"
  };

  const Icon = icons[variant];

  return (
    <GlassModal isOpen={isOpen} onClose={onClose} maxWidth="sm">
      <div className="flex items-center gap-4 mb-4">
        <div className={`p-2 rounded-full bg-white/10 ${colors[variant]}`}>
          <Icon className="w-6 h-6" />
        </div>
        <h2 className="text-lg font-semibold text-white">{title}</h2>
      </div>
      
      <p className="text-gray-300 mb-6">{message}</p>
      
      <div className="flex gap-3 justify-end">
        <Button variant="ghost" onClick={onClose}>
          {cancelText}
        </Button>
        <Button 
          variant={variant === "danger" ? "danger" : "primary"}
          onClick={onClose}
        >
          {confirmText}
        </Button>
      </div>
    </GlassModal>
  );
};

const CreatePinModal = ({ 
  isOpen, 
  onClose 
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: ''
  });

  return (
    <GlassModal isOpen={isOpen} onClose={onClose} maxWidth="2xl">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-full bg-blue-500/20">
            <PlusIcon className="w-6 h-6 text-blue-400" />
          </div>
          <h2 className="text-xl font-semibold text-white">Create New Pin</h2>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-white/10 transition-colors"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">Pin Image</label>
          <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center hover:border-white/30 transition-colors">
            <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-sm text-gray-400">Click to upload or drag and drop</p>
            <p className="text-xs text-gray-500 mt-1">PNG, JPG up to 10MB</p>
          </div>
        </div>
        
        {/* Form Fields */}
        <div className="space-y-4">
          <StandardInput
            label="Pin Title"
            variant="glass"
            placeholder="Enter pin title..."
            value={formData.title}
            onChange={(e) => setFormData({...formData, title: e.target.value})}
            required
          />
          
          <StandardInput
            label="Category"
            variant="glass"
            placeholder="Pin category..."
            value={formData.category}
            onChange={(e) => setFormData({...formData, category: e.target.value})}
          />
          
          <StandardTextarea
            label="Description"
            variant="glass"
            placeholder="Describe your pin..."
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            rows={4}
          />
        </div>
      </div>
      
      <div className="flex gap-3 justify-end mt-6 pt-6 border-t border-white/10">
        <Button variant="ghost" onClick={onClose}>
          Cancel
        </Button>
        <Button variant="primary">
          Create Pin
        </Button>
      </div>
    </GlassModal>
  );
};

const ShareModal = ({ 
  isOpen, 
  onClose 
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [shareUrl] = useState('https://pinpal.app/pin/abc123');

  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl);
    // In real app, show toast notification
  };

  return (
    <GlassModal isOpen={isOpen} onClose={onClose} maxWidth="lg">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 rounded-full bg-green-500/20">
          <ShareIcon className="w-6 h-6 text-green-400" />
        </div>
        <h2 className="text-xl font-semibold text-white">Share Pin</h2>
      </div>
      
      <div className="space-y-6">
        {/* Share URL */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-2">Share Link</label>
          <div className="flex gap-2">
            <Input
              variant="glass"
              value={shareUrl}
              readOnly
              className="flex-1"
            />
            <Button variant="secondary" onClick={copyToClipboard}>
              Copy
            </Button>
          </div>
        </div>
        
        {/* Social Share Options */}
        <div>
          <label className="block text-sm font-medium text-gray-200 mb-3">Share on Social</label>
          <div className="grid grid-cols-3 gap-3">
            <button className="p-4 rounded-lg bg-blue-600/20 hover:bg-blue-600/30 transition-colors text-center">
              <div className="text-blue-400 font-semibold">Facebook</div>
            </button>
            <button className="p-4 rounded-lg bg-sky-500/20 hover:bg-sky-500/30 transition-colors text-center">
              <div className="text-sky-400 font-semibold">Twitter</div>
            </button>
            <button className="p-4 rounded-lg bg-pink-600/20 hover:bg-pink-600/30 transition-colors text-center">
              <div className="text-pink-400 font-semibold">Instagram</div>
            </button>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end mt-6 pt-6 border-t border-white/10">
        <Button variant="ghost" onClick={onClose}>
          Done
        </Button>
      </div>
    </GlassModal>
  );
};

// ===== MAIN STORIES =====

export const ModalSystem: Story = {
  render: () => {
    const [activeModal, setActiveModal] = useState<string | null>(null);

    const openModal = (modalType: string) => setActiveModal(modalType);
    const closeModal = () => setActiveModal(null);

    return (
      <div className="min-h-screen bg-black text-white p-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-indigo-400 to-purple-600 bg-clip-text text-transparent">
              Sistema de Modais
            </h1>
            <p className="text-lg text-gray-300">
              Modais com glass morphism e casos de uso variados
            </p>
          </div>

          {/* Modal Triggers */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div className="bg-gray-900 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Confirmação</h3>
              <p className="text-gray-400 text-sm mb-4">
                Modal de confirmação para ações destrutivas
              </p>
              <Button 
                variant="danger" 
                onClick={() => openModal('delete')}
                className="w-full"
              >
                Delete Item
              </Button>
            </div>

            <div className="bg-gray-900 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Formulário</h3>
              <p className="text-gray-400 text-sm mb-4">
                Modal para criação de novos itens
              </p>
              <Button 
                variant="primary" 
                onClick={() => openModal('create')}
                className="w-full"
              >
                Create Pin
              </Button>
            </div>

            <div className="bg-gray-900 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Compartilhar</h3>
              <p className="text-gray-400 text-sm mb-4">
                Modal para compartilhamento social
              </p>
              <Button 
                variant="secondary" 
                onClick={() => openModal('share')}
                className="w-full"
              >
                Share Pin
              </Button>
            </div>

            <div className="bg-gray-900 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Sucesso</h3>
              <p className="text-gray-400 text-sm mb-4">
                Modal de confirmação de sucesso
              </p>
              <Button 
                variant="primary" 
                onClick={() => openModal('success')}
                className="w-full"
              >
                Complete Action
              </Button>
            </div>

            <div className="bg-gray-900 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Informação</h3>
              <p className="text-gray-400 text-sm mb-4">
                Modal informativo com detalhes
              </p>
              <Button 
                variant="ghost" 
                onClick={() => openModal('info')}
                className="w-full"
              >
                Show Info
              </Button>
            </div>

            <div className="bg-gray-900 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Tamanho Grande</h3>
              <p className="text-gray-400 text-sm mb-4">
                Modal em tamanho grande para conteúdo extenso
              </p>
              <Button 
                variant="secondary" 
                onClick={() => openModal('large')}
                className="w-full"
              >
                Open Large Modal
              </Button>
            </div>
          </div>

          {/* Modal Size Examples */}
          <div className="bg-gray-900 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Tamanhos de Modal</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-xs')}
              >
                XS
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-sm')}
              >
                Small
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-md')}
              >
                Medium
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-lg')}
              >
                Large
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-xl')}
              >
                XL
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-2xl')}
              >
                2XL
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => openModal('size-full')}
              >
                Full
              </Button>
            </div>
          </div>

          {/* Modals */}
          <ConfirmationModal
            isOpen={activeModal === 'delete'}
            onClose={closeModal}
            title="Delete Item"
            message="Are you sure you want to delete this item? This action cannot be undone."
            confirmText="Delete"
            variant="danger"
          />

          <CreatePinModal
            isOpen={activeModal === 'create'}
            onClose={closeModal}
          />

          <ShareModal
            isOpen={activeModal === 'share'}
            onClose={closeModal}
          />

          <ConfirmationModal
            isOpen={activeModal === 'success'}
            onClose={closeModal}
            title="Success!"
            message="Your action has been completed successfully."
            confirmText="Continue"
            cancelText="Close"
            variant="success"
          />

          <ConfirmationModal
            isOpen={activeModal === 'info'}
            onClose={closeModal}
            title="Information"
            message="This is an informational modal with additional details about the current context."
            confirmText="Got it"
            cancelText="Learn More"
            variant="info"
          />

          {/* Size Examples */}
          {(['xs', 'sm', 'md', 'lg', 'xl', '2xl', 'full'] as const).map(maxWidth => (
            <GlassModal
              key={maxWidth}
              isOpen={activeModal === `size-${maxWidth}`}
              onClose={closeModal}
              maxWidth={maxWidth}
            >
              <div>
                <h2 className="text-xl font-semibold text-white mb-4">
                  {maxWidth.toUpperCase()} Modal
                </h2>
                <p className="text-gray-300 mb-6">
                  This is a {maxWidth} sized modal. The content adapts to the available space 
                  while maintaining proper proportions and readability.
                </p>
                <div className="flex justify-end">
                  <Button variant="primary" onClick={closeModal}>
                    Close
                  </Button>
                </div>
              </div>
            </GlassModal>
          ))}

          <GlassModal
            isOpen={activeModal === 'large'}
            onClose={closeModal}
            maxWidth="2xl"
          >
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">
                Large Content Modal
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div>
                  <h3 className="text-lg font-medium text-white mb-3">Section 1</h3>
                  <p className="text-gray-300 mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
                    tempor incididunt ut labore et dolore magna aliqua.
                  </p>
                  <StandardInput
                    label="Field 1"
                    variant="glass"
                    placeholder="Enter value..."
                  />
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-white mb-3">Section 2</h3>
                  <p className="text-gray-300 mb-4">
                    Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris 
                    nisi ut aliquip ex ea commodo consequat.
                  </p>
                  <StandardTextarea
                    label="Description"
                    variant="glass"
                    placeholder="Enter description..."
                    rows={3}
                  />
                </div>
              </div>
              
              <div className="flex gap-3 justify-end pt-6 border-t border-white/10">
                <Button variant="ghost" onClick={closeModal}>
                  Cancel
                </Button>
                <Button variant="primary">
                  Save Changes
                </Button>
              </div>
            </div>
          </GlassModal>
        </div>
      </div>
    );
  },
};

export const GlassModalShowcase: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div className="p-8 bg-gradient-to-br from-blue-900 to-purple-900 min-h-screen">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Glass Modal Effect</h2>
          <p className="text-blue-100 mb-8">
            Modal com efeito glassmorphism sobre fundo colorido
          </p>
          <Button variant="primary" onClick={() => setIsOpen(true)}>
            Open Glass Modal
          </Button>
        </div>

        <GlassModal isOpen={isOpen} onClose={() => setIsOpen(false)} maxWidth="md">
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">
              Glass Morphism Effect
            </h3>
            <p className="text-gray-300 mb-6">
              Este modal demonstra o efeito glass morphism com backdrop blur,
              transparência e bordas sutis que criam uma sensação de profundidade
              e modernidade.
            </p>
            <div className="space-y-4 mb-6">
              <StandardInput
                label="Nome"
                variant="glass"
                placeholder="Digite seu nome..."
              />
              <StandardInput
                label="Email"
                variant="glass"
                placeholder="Digite seu email..."
              />
            </div>
            <div className="flex gap-3 justify-end">
              <Button variant="ghost" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button variant="primary">
                Submit
              </Button>
            </div>
          </div>
        </GlassModal>
      </div>
    );
  },
};

export const CleanModalShowcase: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div className="p-8 bg-gray-100 dark:bg-gray-900 min-h-screen">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Clean Modal Style</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-8">
            Modal com fundo sólido limpo como na imagem de referência
          </p>
          <Button variant="primary" onClick={() => setIsOpen(true)}>
            Open Clean Modal
          </Button>
        </div>

        <GlassModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          maxWidth="md"
          variant="clean"
          title="Checkout with Meta"
        >
          <div className="space-y-6">
            {/* Product Info */}
            <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">$548.48</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">Amanda Peattie</h4>
                <p className="text-gray-600 dark:text-gray-300">Product Item</p>
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contact
                </label>
                <input
                  type="email"
                  value="<EMAIL>"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Ship to
                </label>
                <div className="text-gray-900 dark:text-white">
                  <div className="font-medium">Priscilla Tavares</div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    10363 Windermere Chase Blvd<br />
                    Gotha, FL 34734
                  </div>
                </div>
              </div>
            </div>

            {/* Action Button */}
            <Button variant="primary" className="w-full">
              Continue
            </Button>
          </div>
        </GlassModal>
      </div>
    );
  },
};

export const ModalSizes: Story = {
  render: () => {
    const [activeSize, setActiveSize] = useState<string | null>(null);
    
    const sizes = [
      { key: 'xs', label: 'Extra Small', description: 'Para avisos mínimos' },
      { key: 'sm', label: 'Small', description: 'Para confirmações simples' },
      { key: 'md', label: 'Medium', description: 'Tamanho padrão' },
      { key: 'lg', label: 'Large', description: 'Para formulários' },
      { key: 'xl', label: 'Extra Large', description: 'Conteúdo extenso' },
      { key: '2xl', label: '2X Large', description: 'Para editores' },
      { key: 'full', label: 'Full Screen', description: 'Visualizadores' },
    ] as const;

    return (
      <div className="p-8 bg-gray-900 min-h-screen">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Modal Sizes</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sizes.map(size => (
              <div key={size.key} className="bg-gray-800 p-4 rounded-lg">
                <h3 className="font-semibold text-white mb-1">{size.label}</h3>
                <p className="text-gray-400 text-sm mb-3">{size.description}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setActiveSize(size.key)}
                  className="w-full"
                >
                  Open {size.key.toUpperCase()}
                </Button>
              </div>
            ))}
          </div>

          {sizes.map(size => (
            <GlassModal
              key={size.key}
              isOpen={activeSize === size.key}
              onClose={() => setActiveSize(null)}
              maxWidth={size.key}
            >
              <div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  {size.label} Modal
                </h3>
                <p className="text-gray-300 mb-4">
                  {size.description}. Este modal demonstra o tamanho {size.key} 
                  com conteúdo apropriado para este formato.
                </p>
                
                {size.key !== 'xs' && (
                  <div className="space-y-4 mb-6">
                    <StandardInput
                      label="Exemplo de Campo"
                      variant="glass"
                      placeholder="Digite algo..."
                    />
                    {['lg', 'xl', '2xl', 'full'].includes(size.key) && (
                      <StandardTextarea
                        label="Descrição"
                        variant="glass"
                        placeholder="Adicione uma descrição..."
                        rows={3}
                      />
                    )}
                  </div>
                )}
                
                <div className="flex gap-3 justify-end">
                  <Button variant="ghost" onClick={() => setActiveSize(null)}>
                    Close
                  </Button>
                  <Button variant="primary">
                    Action
                  </Button>
                </div>
              </div>
            </GlassModal>
          ))}
        </div>
      </div>
    );
  },
}; 