import React, { Suspense } from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// Lazy load the UserProfile component for better performance
const UserProfile = React.lazy(() => 
  import('@/modules/user/components/UserProfile').then(module => ({
    default: module.UserProfile
  }))
);

export const ProfilePage: React.FC = () => {
  return (
    <Suspense 
      fallback={
        <div className="min-h-screen bg-white dark:bg-black flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      }
    >
      <UserProfile />
    </Suspense>
  );
}; 