// Import CORS prevention FIRST - before anything else
// Temporarily disabled all CORS scripts to fix Firebase Auth
// import './scripts/smartCorsProtection';

import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import App from './App.tsx';
import './index.css';

// Import and inject critical CSS (consolidated tokens)
// TEMPORARILY DISABLED TO DEBUG SIDEBAR BACKGROUND ISSUE
// import { injectCriticalCSS } from '@/components/ui/design-system/foundations';
// injectCriticalCSS();

// Import Web Vitals monitoring for performance tracking
import './lib/webVitals';

// Register Service Worker for performance optimization
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });
      
      console.log('🚀 SW: Service Worker registered successfully:', registration.scope);
      
      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('🔄 SW: New content available, please refresh');
              // Optionally show a toast to the user about the update
            }
          });
        }
      });
      
    } catch (error) {
      console.error('❌ SW: Service Worker registration failed:', error);
    }
  });
}

// Import development scripts for console access
if (import.meta.env.DEV) {
  import('./scripts/clearUsers');
  import('./scripts/syncCheckInsMetrics');
  import('./scripts/checkDuplicateUsers');
}

// Temporarily disabled all CORS prevention to fix Firebase Auth
// import './utils/googleMapsConfig';

// Suprimir erros comuns de extensões do navegador
const originalConsoleError = console.error;
console.error = (...args) => {
  const message = args[0]?.toString() || '';
  
  // Filtrar erros conhecidos de extensões e Firebase Auth popup
  const ignoredErrors = [
    'A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received',
    'Extension context invalidated',
    'message channel closed',
    'Could not establish connection',
    'The message port closed before a response was received',
    'Cross-Origin-Opener-Policy policy would block the window.closed call',
    'Cross-Origin-Opener-Policy policy would block the window.close call'
  ];
  
  const shouldIgnore = ignoredErrors.some(ignored => 
    message.includes(ignored)
  );
  
  if (!shouldIgnore) {
    originalConsoleError.apply(console, args);
  }
};

// Suprimir erros de Promise rejeitadas relacionadas a extensões
window.addEventListener('unhandledrejection', (event) => {
  const message = event.reason?.message || event.reason?.toString() || '';
  
  const ignoredPromiseErrors = [
    'message channel closed',
    'Extension context invalidated',
    'Could not establish connection',
    'A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received'
  ];
  
  const shouldIgnore = ignoredPromiseErrors.some(ignored => 
    message.includes(ignored)
  );
  
  if (shouldIgnore) {
    event.preventDefault(); // Previne o erro no console
  }
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <App />
    </BrowserRouter>
  </React.StrictMode>,
); 