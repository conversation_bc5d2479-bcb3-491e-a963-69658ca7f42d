import { useTheme } from './ThemeProvider';
import { colors, borderRadius, spacing, shadows, typography, message } from './tokens';

/**
 * Hook para acessar tokens de design com suporte para temas
 * 
 * Este hook facilita o acesso aos tokens de design do sistema,
 * automaticamente considerando o tema atual (claro/escuro)
 */
export const useDesignTokens = () => {
  const { isDark } = useTheme();
  const isDarkMode = isDark;
  
  // Retorna um objeto com todos os tokens de design
  return {
    colors,
    borderRadius,
    spacing,
    shadows,
    typography,
    message,
    isDarkMode,
    
    // Helper para cores semânticas
    semantic: {
      // Backgrounds primários
      background: {
        primary: isDarkMode ? colors.gray[900] : colors.gray[50],
        secondary: isDarkMode ? colors.gray[800] : colors.gray[100],
        tertiary: isDarkMode ? colors.gray[700] : colors.gray[200],
      },
      // Cores de texto
      text: {
        primary: isDarkMode ? colors.gray[100] : colors.gray[900],
        secondary: isDarkMode ? colors.gray[300] : colors.gray[700],
        muted: isDarkMode ? colors.gray[400] : colors.gray[500],
        inverse: isDarkMode ? colors.gray[900] : colors.gray[100],
      },
      // Cores de borda
      border: {
        light: isDarkMode ? colors.gray[700] : colors.gray[200],
        medium: isDarkMode ? colors.gray[600] : colors.gray[300],
        dark: isDarkMode ? colors.gray[500] : colors.gray[400],
      },
      // Cores de ação
      action: {
        primary: colors.primary[500],
        primaryHover: isDarkMode ? colors.primary[400] : colors.primary[600],
        secondary: isDarkMode ? colors.gray[700] : colors.gray[200],
        secondaryHover: isDarkMode ? colors.gray[600] : colors.gray[300],
      },
      // Estado
      state: {
        success: colors.success.main,
        error: colors.error.main,
        warning: colors.warning.main,
        info: colors.info.main,
      }
    },
    
    // Helper para sombras contextuais
    getShadow: (size: 'sm' | 'md' | 'lg' | 'xl') => {
      return shadows[size];
    },
    
    // Helper para espaçamentos
    getSpacing: (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl') => {
      return spacing[size];
    },
    
    // Helper para fontes
    getFont: (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl', weight: 'regular' | 'medium' | 'semibold' | 'bold' = 'regular') => {
      return {
        fontSize: typography.fontSize[size],
        fontWeight: typography.fontWeight[weight],
        fontFamily: typography.fontFamily.body,
      };
    },
  };
}; 