import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { useToast } from '@/hooks/useToast';
import { auth, googleProvider } from '@/config/firebase';
import { createUserWithEmailAndPassword, signInWithPopup, updateProfile } from 'firebase/auth';
import { Input } from '@/components/ui/design-system/Input';
import { Button } from '@/components/ui/design-system/Button';
import { UsernameValidator } from '@/components/ui/UsernameValidator';
import { EmailValidator } from '@/components/ui/EmailValidator';
import { EnvelopeIcon, LockClosedIcon, UserIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { authApiService } from '@/services/api/authApiService';
import { UsernameService } from '@/services/usernameService';
import { authTokens, authCardClassesSolid } from '@/theme/authTokens';

interface SignupFormData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username: string;
}

export const Signup = () => {
  console.log('🚨 SIGNUP COMPONENT LOADED!!! LOOK FOR THIS MESSAGE!!!');
  
  const navigate = useNavigate();
  const { setUser, isAuthenticated, authLoaded, permissions, adminUser } = useAuthStore();
  const { success, error } = useToast();

  // Redirect if already authenticated
  useEffect(() => {
    if (authLoaded && isAuthenticated) {
      console.log('🔄 User already authenticated, redirecting from signup...');
      
      // Check if user has admin access and redirect accordingly
      if (permissions.canAccessAdmin || adminUser?.role === 'admin') {
        console.log('🛡️ Redirecting to admin area');
        navigate('/admin', { replace: true });
      } else {
        console.log('🏠 Redirecting to dashboard');
        navigate('/dashboard', { replace: true });
      }
    }
  }, [authLoaded, isAuthenticated, permissions, adminUser, navigate]);
  const [formData, setFormData] = useState<SignupFormData>({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    username: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validar username antes de criar a conta
    if (!formData.username) {
      error('Please choose a username');
      return;
    }
    
    const usernameValidation = UsernameService.validateUsername(formData.username);
    if (!usernameValidation.valid) {
      error(usernameValidation.error || 'Invalid username');
      return;
    }
    
    const usernameAvailability = await UsernameService.checkAvailability(formData.username);
    if (!usernameAvailability.available) {
      error('Username is not available');
      return;
    }
    
    // Validação básica de senha
    if (formData.password.length < 6) {
      error('Password must be at least 6 characters long');
      return;
    }
    
    try {
      console.log('🔐 Creating Firebase Auth user with:', {
        email: formData.email,
        hasPassword: !!formData.password,
        passwordLength: formData.password.length
      });
      
      const result = await createUserWithEmailAndPassword(auth, formData.email, formData.password);
      const user = result.user;
      
      console.log('✅ Firebase Auth user created successfully:', user.uid);
      
      const fullName = `${formData.firstName} ${formData.lastName}`.trim();
      await updateProfile(user, { displayName: fullName });
      
      console.log('🔄 Syncing with PostgreSQL...');
      
      // Sync with PostgreSQL
      await authApiService.syncUserWithPostgreSQL(user, {
        username: formData.username,
        firstName: formData.firstName,
        lastName: formData.lastName
      });
      
      console.log('✅ PostgreSQL sync completed');
      
      // Create default boards for the new user
      console.log('🔄 Creating default boards...');
      try {
        const { boardsService } = await import('@/services/boardsService');
        await boardsService.createDefaultBoards(user.uid);
        console.log('✅ Default boards created successfully');
      } catch (boardError) {
        console.error('❌ Failed to create default boards:', boardError);
        // Don't fail the signup process if board creation fails
      }
      
      setUser({
        id: user.uid,
        email: user.email || '',
        firstName: formData.firstName,
        lastName: formData.lastName,
        username: formData.username,
        avatarUrl: user.photoURL || '',
        emailVerified: user.emailVerified,
        isActive: true,
        isVerified: user.emailVerified,
        role: 'user',
        status: 'active',
        preferences: {
          notificationsEnabled: true,
          publicProfile: true,
          showLocation: false,
          showEmail: false,
          allowMessages: true,
          allowComments: true
        },
        stats: {
          pinsCount: 0,
          boardsCount: 3, // Updated to reflect default boards
          followersCount: 0,
          followingCount: 0,
          checkInsCount: 0,
          tradesCompletedCount: 0,
          likesReceivedCount: 0
        },
        joinedAt: new Date() as any,
        createdAt: new Date() as any,
        updatedAt: new Date() as any,
        lastLoginAt: new Date() as any,
      });
      success('Signup successful! Your boards are ready to use.');
      navigate('/');
    } catch (signupError: any) {
      console.error('❌ Signup error:', signupError);
      
      // Tratamento específico de erros do Firebase Auth
      let errorMessage = 'Signup failed';
      
      if (signupError.code) {
        switch (signupError.code) {
          case 'auth/email-already-in-use':
            errorMessage = 'This email is already registered. Try logging in instead.';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Please enter a valid email address.';
            break;
          case 'auth/weak-password':
            errorMessage = 'Password is too weak. Please choose a stronger password.';
            break;
          case 'auth/operation-not-allowed':
            errorMessage = 'Email/password accounts are not enabled. Please contact support.';
            break;
          case 'auth/network-request-failed':
            errorMessage = 'Network error. Please check your internet connection.';
            break;
          case 'auth/invalid-api-key':
            errorMessage = 'Configuration error. Please contact support.';
            break;
          case 'auth/app-deleted':
            errorMessage = 'Application configuration error. Please contact support.';
            break;
          case 'auth/unauthorized-domain':
            errorMessage = 'This domain is not authorized. Please contact support.';
            break;
          default:
            errorMessage = `Error: ${signupError.code} - ${signupError.message}`;
        }
      } else {
        errorMessage = signupError.message || 'An unexpected error occurred';
      }
      
      error(errorMessage);
    }
  };

  const handleGoogleSignup = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;
      
      // Sync with PostgreSQL
      await authApiService.syncUserWithPostgreSQL(user, {
        username: (user.displayName || 'user').toLowerCase().replace(/\s+/g, '-'),
        firstName: user.displayName?.split(' ')[0] || 'User',
        lastName: user.displayName?.split(' ').slice(1).join(' ') || ''
      });
      
      // Create default boards for the new user
      console.log('🔄 Creating default boards...');
      try {
        const { boardsService } = await import('@/services/boardsService');
        await boardsService.createDefaultBoards(user.uid);
        console.log('✅ Default boards created successfully');
      } catch (boardError) {
        console.error('❌ Failed to create default boards:', boardError);
        // Don't fail the signup process if board creation fails
      }
      
      setUser({
        id: user.uid,
        email: user.email || '',
         firstName: user.displayName?.split(' ')[0] || 'User',
         lastName: user.displayName?.split(' ').slice(1).join(' ') || '',
         username: (user.displayName || 'user').toLowerCase().replace(/\s+/g, '-'),
        avatarUrl: user.photoURL || '',
        emailVerified: user.emailVerified,
         isActive: true,
         isVerified: user.emailVerified,
         role: 'user',
         status: 'active',
         preferences: {
           notificationsEnabled: true,
           publicProfile: true,
           showLocation: false,
           showEmail: false,
           allowMessages: true,
           allowComments: true
         },
         stats: {
          pinsCount: 0,
          boardsCount: 3, // Updated to reflect default boards
          followersCount: 0,
          followingCount: 0,
          checkInsCount: 0,
          tradesCompletedCount: 0,
          likesReceivedCount: 0
        },
         joinedAt: new Date() as any,
         createdAt: new Date() as any,
         updatedAt: new Date() as any,
         lastLoginAt: new Date() as any,
       });
      success('Signup with Google successful! Your boards are ready to use.');
      navigate('/');
    } catch (googleError) {
      error('Google signup failed');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEmailChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      email: value,
    }));
  };

  const handleUsernameChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      username: value,
    }));
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className={`min-h-screen flex items-center justify-center ${authTokens.background.page}`}>
      <div className={`w-full sm:max-w-md ${authCardClassesSolid} min-h-screen sm:min-h-0 flex flex-col justify-center ${authTokens.transition.default}`}>

        <h2 className={`text-2xl sm:text-3xl font-bold ${authTokens.text.primary} mb-2`}>Create your account</h2>
        <p className={`${authTokens.text.secondary} mb-8 text-sm sm:text-base`}>Create your account for free and start using our app.</p>
        
        <Button
          onClick={handleGoogleSignup}
          variant="secondary"
          size="lg"
          className="w-full mb-6 text-base sm:text-lg"
        >
          <svg className='w-5 h-5 mr-2' viewBox='0 0 48 48'>
            <g>
              <path fill='#4285F4' d='M24 9.5c3.54 0 6.36 1.46 8.09 2.69l6-5.84C34.91 2.36 29.86 0 24 0 14.82 0 6.71 5.82 2.69 14.09l7.37 5.72C12.09 13.36 17.55 9.5 24 9.5z'/>
              <path fill='#34A853' d='M46.1 24.55c0-1.64-.15-3.22-.43-4.74H24v9.01h12.42c-.54 2.91-2.18 5.38-4.65 7.04l7.19 5.59C43.91 37.09 46.1 31.27 46.1 24.55z'/>
              <path fill='#FBBC05' d='M10.06 28.36A14.5 14.5 0 019.5 24c0-1.52.26-2.99.73-4.36l-7.37-5.72A23.93 23.93 0 000 24c0 3.77.9 7.34 2.5 10.45l7.56-6.09z'/>
              <path fill='#EA4335' d='M24 48c6.48 0 11.92-2.14 15.89-5.82l-7.19-5.59c-2 1.36-4.56 2.18-8.7 2.18-6.45 0-11.91-3.86-14.05-9.36l-7.56 6.09C6.71 42.18 14.82 48 24 48z'/>
              <path fill='none' d='M0 0h48v48H0z'/>
            </g>
          </svg>
          Sign Up with Google
        </Button>
        
        <div className="flex items-center my-6">
          <div className={`flex-1 h-px ${authTokens.border.subtle}`} />
          <span className={`mx-4 ${authTokens.text.muted} text-xs sm:text-sm`}>Or Sign up with Email</span>
          <div className={`flex-1 h-px ${authTokens.border.subtle}`} />
        </div>
        <form className="space-y-5" onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-3">
            <Input
              variant="auth"
              label="First Name"
              id="firstName"
              name="firstName"
              type="text"
              required
              placeholder="First name"
              value={formData.firstName}
              onChange={handleChange}
            />
            <Input
              variant="auth"
              label="Last Name"
              id="lastName"
              name="lastName"
              type="text"
              required
              placeholder="Last name"
              value={formData.lastName}
              onChange={handleChange}
            />
          </div>
          <EmailValidator
            value={formData.email}
            onChange={handleEmailChange}
            required
          />
          <UsernameValidator
            value={formData.username}
            onChange={handleUsernameChange}
            fullName={`${formData.firstName} ${formData.lastName}`.trim()}
            required
            autoSuggest={true}
          />
          <Input
            variant="auth"
            label="Password"
            rightIcon={
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
              </button>
            }
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="new-password"
            required
            placeholder="Password"
            value={formData.password}
            onChange={handleChange}
          />
          <Button
            type="submit"
            variant="primary"
            size="lg"
            className="w-full text-lg font-semibold"
          >
            Sign up
          </Button>
        </form>
        <div className={`text-center mt-6 text-sm ${authTokens.text.secondary}`}>
          Already have an account?{' '}
          <Link to="/login" className={`${authTokens.text.link} font-medium hover:underline`}>
            Sign In
          </Link>
        </div>
      </div>
    </div>
  );
}; 