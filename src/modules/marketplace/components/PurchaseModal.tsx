import React, { useState, useEffect } from 'react';
import { CurrencyDollarIcon, ShieldCheckIcon, TruckIcon, ClockIcon, ChevronDownIcon, ChevronUpIcon, PlusIcon, PencilIcon, TrashIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import { GlassModal } from '@/components/ui/GlassModal';
import { Button } from '@/components/ui/design-system/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Avatar } from '@/components/ui/Avatar';
import { useToast } from '@/hooks/useToast';
import { useAuthStore } from '@/store/authStore';
import { useShippingAddresses } from '@/hooks/useShippingAddresses';
import { useContactEmails } from '@/hooks/useContactEmails';
import { AddShippingAddressModal } from './AddShippingAddressModal';
import { EditShippingAddressModal } from './EditShippingAddressModal';
import { AddEmailModal } from '../../../components/ui/modals/AddEmailModal';
import { EditEmailModal } from './EditEmailModal';
import { StripePaymentElement } from './StripePaymentElement';
import { ConfirmRemoveModal } from '@/components/ui/ConfirmRemoveModal';
import { AddBillingAddressModal } from './AddBillingAddressModal';
import type { MarketplacePin } from '@/types/marketplace';
import type { ShippingAddress } from '@/types/shopping';

interface PricePreview {
  pinPrice: number;
  shipping: number;
  serviceCharge: number;
  finalPrice: number;
}

interface PurchaseModalProps {
  pin: MarketplacePin;
  isOpen: boolean;
  onClose: () => void;
  onPurchaseSuccess: () => void;
}

// Stripe Logo Component
const StripeLogo: React.FC<{ className?: string }> = ({ className = "h-8" }) => (
  <svg 
    version="1.1" 
    id="Layer_1" 
    xmlns="http://www.w3.org/2000/svg" 
    x="0" 
    y="0" 
    viewBox="0 0 468 222.5" 
    className={className}
    xmlSpace="preserve"
  >
    <style>{`.st0{fill-rule:evenodd;clip-rule:evenodd;fill:currentColor}`}</style>
    <path 
      className="st0" 
      d="M414 113.4c0-25.6-12.4-45.8-36.1-45.8-23.8 0-38.2 20.2-38.2 45.6 0 30.1 17 45.3 41.4 45.3 11.9 0 20.9-2.7 27.7-6.5v-20c-6.8 3.4-14.6 5.5-24.5 5.5-9.7 0-18.3-3.4-19.4-15.2h48.9c0-1.3.2-6.5.2-8.9zm-49.4-9.5c0-11.3 6.9-16 13.2-16 6.1 0 12.6 4.7 12.6 16h-25.8zM301.1 67.6c-9.8 0-16.1 4.6-19.6 7.8l-1.3-6.2h-22v116.6l25-5.3.1-28.3c3.6 2.6 8.9 6.3 17.7 6.3 17.9 0 34.2-14.4 34.2-46.1-.1-29-16.6-44.8-34.1-44.8zm-6 68.9c-5.9 0-9.4-2.1-11.8-4.7l-.1-37.1c2.6-2.9 6.2-4.9 11.9-4.9 9.1 0 15.4 10.2 15.4 23.3 0 13.4-6.2 23.4-15.4 23.4zM223.8 61.7l25.1-5.4V36l-25.1 5.3zM223.8 69.3h25.1v87.5h-25.1zM196.9 76.7l-1.6-7.4h-21.6v87.5h25V97.5c5.9-7.7 15.9-6.3 19-5.2v-23c-3.2-1.2-14.9-3.4-20.8 7.4zM146.9 47.6l-24.4 5.2-.1 80.1c0 14.8 11.1 25.7 25.9 25.7 8.2 0 14.2-1.5 17.5-3.3V135c-3.2 1.3-19 5.9-19-8.9V90.6h19V69.3h-19l.1-21.7zM79.3 94.7c0-3.9 3.2-5.4 8.5-5.4 7.6 0 17.2 2.3 24.8 6.4V72.2c-8.3-3.3-16.5-4.6-24.8-4.6C67.5 67.6 54 78.2 54 95.9c0 27.6 38 23.2 38 35.1 0 4.6-4 6.1-9.6 6.1-8.3 0-18.9-3.4-27.3-8v23.8c9.3 4 18.7 5.7 27.3 5.7 20.8 0 35.1-10.3 35.1-28.2-.1-29.8-38.2-24.5-38.2-35.7z"
    />
  </svg>
);

export const PurchaseModal: React.FC<PurchaseModalProps> = ({
  pin,
  isOpen,
  onClose,
  onPurchaseSuccess
}) => {
  const { user } = useAuthStore();
  const { showToast } = useToast();
  const [pricePreview, setPricePreview] = useState<PricePreview | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  
  // ✅ NOVOS ESTADOS PARA STRIPE SEGURO
  const [clientSecret, setClientSecret] = useState<string>('');
  const [orderId, setOrderId] = useState<string>('');
  const [paymentStep, setPaymentStep] = useState<'setup' | 'payment' | 'processing' | 'success'>('setup');
  
  // 📦 ESTADOS PARA CONTROLE DE ESTOQUE
  const [stockStatus, setStockStatus] = useState<{
    isInStock: boolean;
    quantity: number;
    lastChecked: Date | null;
    isChecking: boolean;
  }>({
    isInStock: true,
    quantity: pin?.quantity || 0,
    lastChecked: null,
    isChecking: false
  });
  
  // Shipping addresses hook
  const { 
    addresses, 
    isLoading: isLoadingAddresses, 
    addAddress,
    updateAddress,
    deleteAddress 
  } = useShippingAddresses(user?.id || '');
  
  // Contact emails hook
  const { 
    emails: contactEmails, 
    isLoading: isLoadingEmails, 
    addEmail,
    updateEmail,
    deleteEmail: removeEmail 
  } = useContactEmails(user?.id || '');
  
  // Form states
  const [selectedContact, setSelectedContact] = useState('');
  const [showAddEmailModal, setShowAddEmailModal] = useState(false);
  const [selectedAddressId, setSelectedAddressId] = useState<string>('');
  const [showAddAddressModal, setShowAddAddressModal] = useState(false);
  const [showEditAddressModal, setShowEditAddressModal] = useState(false);
  const [editingAddress, setEditingAddress] = useState<ShippingAddress | null>(null);
  const [showEditEmailModal, setShowEditEmailModal] = useState(false);
  const [editingEmail, setEditingEmail] = useState<any>(null);
  
  // Billing address states
  const [showAddBillingModal, setShowAddBillingModal] = useState(false);
  const [billingAddresses, setBillingAddresses] = useState<any[]>([]);
  const [selectedBillingId, setSelectedBillingId] = useState<string>('');
  const [expandedSections, setExpandedSections] = useState({
    contact: false,
    shipping: false,
    delivery: false,
    payment: true, // Expandir automaticamente para mostrar o Stripe
    orderSummary: false
  });

  // Confirmation modals
  const [confirmRemoveEmail, setConfirmRemoveEmail] = useState<{
    isOpen: boolean;
    emailId: string;
    emailAddress: string;
    emailName?: string;
  }>({
    isOpen: false,
    emailId: '',
    emailAddress: '',
    emailName: undefined
  });

  const [confirmRemoveAddress, setConfirmRemoveAddress] = useState<{
    isOpen: boolean;
    addressId: string;
    addressName: string;
    fullAddress: string;
  }>({
    isOpen: false,
    addressId: '',
    addressName: '',
    fullAddress: ''
  });

  // Set default address when addresses load
  useEffect(() => {
    console.log('Addresses updated:', addresses);
    console.log('Current selectedAddressId:', selectedAddressId);
    
    if (addresses.length > 0 && !selectedAddressId) {
      const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0];
      console.log('Setting default address:', defaultAddress);
      setSelectedAddressId(defaultAddress.id);
    }
  }, [addresses, selectedAddressId]);

  // Set default contact email when emails load
  useEffect(() => {
    console.log('Contact emails updated:', contactEmails);
    console.log('Current selectedContact:', selectedContact);
    
    if (contactEmails.length > 0 && !selectedContact) {
      const primaryEmail = contactEmails.find(email => email.isPrimary) || contactEmails[0];
      console.log('Setting default contact email:', primaryEmail);
      setSelectedContact(primaryEmail.email);
    }
  }, [contactEmails, selectedContact]);

  // Load price preview when modal opens
  useEffect(() => {
    if (isOpen && pin) {
      // Reset payment state when modal opens
      setPaymentStep('setup');
      setClientSecret('');
      setOrderId('');
      loadPricePreview();
    }
  }, [isOpen, pin?.id]);

  // ✅ CRIAR PAYMENT INTENT QUANDO MODAL ABRE
  useEffect(() => {
    console.log('🔄 Payment Intent Effect:', {
      isOpen,
      hasUser: !!user,
      userId: user?.id,
      selectedAddressId,
      hasPricePreview: !!pricePreview,
      pricePreview,
      paymentStep,
      pinId: pin?.id,
      pinName: pin?.name
    });
    
    if (isOpen && user && selectedAddressId && pricePreview && paymentStep === 'setup') {
      console.log('✅ All conditions met - Calling createPaymentIntent...');
      createPaymentIntent();
    } else {
      console.log('❌ Conditions not met for createPaymentIntent:', {
        isOpen: !!isOpen,
        hasUser: !!user,
        hasSelectedAddress: !!selectedAddressId,
        hasPricePreview: !!pricePreview,
        isSetupStep: paymentStep === 'setup'
      });
    }
  }, [isOpen, user, selectedAddressId, pricePreview, paymentStep]);

  // Get selected address object
  const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);

  const handleAddAddress = async (newAddress: ShippingAddress) => {
    console.log('New address received:', newAddress);
    
    // Close modal first
    setShowAddAddressModal(false);
    
    // Select the newly added address with a small delay to ensure state is updated
    setTimeout(() => {
      setSelectedAddressId(newAddress.id);
      console.log('Selected address ID set to:', newAddress.id);
      console.log('Current addresses:', addresses);
    }, 50);
  };

  const handleDeleteAddress = async (addressId: string) => {
    const address = addresses.find(addr => addr.id === addressId);
    if (address) {
      setConfirmRemoveAddress({
        isOpen: true,
        addressId: address.id,
        addressName: address.addressName,
        fullAddress: `${address.addressLine1}, ${address.city}, ${address.state} ${address.postalCode}`
      });
    }
  };

  const handleSetAsDefault = async (addressId: string) => {
    try {
      console.log('🎯 Setting address as default:', addressId);
      console.log('📋 Current addresses before update:', addresses);
      
      await updateAddress(addressId, { isDefault: true });
      
      console.log('📋 Current addresses after update:', addresses);
      showToast('Default address updated successfully', 'success');
    } catch (error: any) {
      console.error('❌ Error setting default address:', error);
      showToast(error.message || 'Failed to set default address', 'error');
    }
  };

  // ✅ EDIT ADDRESS FUNCTIONS
  const handleEditAddress = (addressId: string) => {
    const address = addresses.find(addr => addr.id === addressId);
    if (address) {
      setEditingAddress(address);
      setShowEditAddressModal(true);
    }
  };

  const handleAddressUpdated = (updatedAddress: ShippingAddress) => {
    // Address will be automatically updated by the hook's refetch
    setShowEditAddressModal(false);
    setEditingAddress(null);
  };

  const handleConfirmDeleteAddress = async () => {
    try {
      console.log('🗑️ Confirming address deletion:', confirmRemoveAddress.addressId);
      console.log('📋 Current addresses before deletion:', addresses);
      
      await deleteAddress(confirmRemoveAddress.addressId);
      showToast('Address removed successfully', 'success');
      
      // ✅ IMPROVED LOGIC: Handle address selection after deletion
      if (selectedAddressId === confirmRemoveAddress.addressId) {
        console.log('🔄 Deleted address was selected, need to select another');
        
        // Wait a bit for the hook to update the addresses state
        setTimeout(() => {
          const remainingAddresses = addresses.filter(addr => addr.id !== confirmRemoveAddress.addressId);
          console.log('📋 Remaining addresses after deletion:', remainingAddresses);
          
          if (remainingAddresses.length > 0) {
            // If only one address remains, it will be automatically set as default by the hook
            // Select the default address (either existing default or the only remaining one)
            const defaultAddress = remainingAddresses.find(addr => addr.isDefault) || remainingAddresses[0];
            console.log('🏠 Selecting address:', defaultAddress.id, '(isDefault:', defaultAddress.isDefault, ')');
            setSelectedAddressId(defaultAddress.id);
          } else {
            console.log('📭 No addresses remaining');
            setSelectedAddressId('');
          }
        }, 100); // Small delay to allow state update
      }

      // Close confirmation modal
      setConfirmRemoveAddress({
        isOpen: false,
        addressId: '',
        addressName: '',
        fullAddress: ''
      });
    } catch (error: any) {
      console.error('❌ Error deleting address:', error);
      showToast(error.message || 'Failed to remove address', 'error');
    }
  };

  const handleAddEmail = async (email: string) => {
    try {
      const newContactEmail = await addEmail(email);
      setSelectedContact(newContactEmail.email);
      setShowAddEmailModal(false);
      showToast('Email added successfully', 'success');
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleEditEmail = (emailId: string) => {
    const email = contactEmails.find(e => e.id === emailId);
    if (email) {
      setEditingEmail(email);
      setShowEditEmailModal(true);
    }
  };

  const handleEmailUpdated = (updatedEmail: any) => {
    setEditingEmail(null);
    setShowEditEmailModal(false);
    showToast('Email updated successfully', 'success');
  };

  const handleRemoveEmailClick = (emailId: string) => {
    const email = contactEmails.find(e => e.id === emailId);
    if (email) {
      setConfirmRemoveEmail({
        isOpen: true,
        emailId: email.id,
        emailAddress: email.email,
        emailName: email.emailName
      });
    }
  };

  const handleRemoveEmail = async () => {
    try {
      await removeEmail(confirmRemoveEmail.emailId);
      
      // If removed email was selected, select another one
      const removedEmail = contactEmails.find(email => email.id === confirmRemoveEmail.emailId);
      if (removedEmail && selectedContact === removedEmail.email) {
        const remainingEmails = contactEmails.filter(email => email.id !== confirmRemoveEmail.emailId);
        if (remainingEmails.length > 0) {
          setSelectedContact(remainingEmails[0].email);
        } else {
          setSelectedContact('');
        }
      }

      // Close confirmation modal
      setConfirmRemoveEmail({
        isOpen: false,
        emailId: '',
        emailAddress: '',
        emailName: undefined
      });
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  // Billing address functions
  const handleAddBillingAddress = async (newBillingAddress: any) => {
    console.log('🏠 Adding new billing address:', newBillingAddress);
    console.log('📋 Current billing addresses before:', billingAddresses);
    
    setBillingAddresses(prev => {
      const updated = [...prev, newBillingAddress];
      console.log('📋 Updated billing addresses:', updated);
      return updated;
    });
    
    setSelectedBillingId(newBillingAddress.id);
    console.log('✅ Selected billing ID set to:', newBillingAddress.id);
    setShowAddBillingModal(false);
  };

  // 📦 FUNÇÃO CRÍTICA: Verificar estoque em tempo real
  const checkStockAvailability = async () => {
    if (!pin?.id) return false;
    
    setStockStatus(prev => ({ ...prev, isChecking: true }));
    
    try {
      console.log(`🔍 Checking stock for pin ${pin.id}...`);
      
      const response = await fetch(`/api/marketplace/pins/${pin.id}/stock`);
      const data = await response.json();
      
      if (data.success) {
        const { stock } = data;
        console.log(`📦 Stock check result:`, stock);
        
        setStockStatus({
          isInStock: stock.isInStock,
          quantity: stock.quantity,
          lastChecked: new Date(),
          isChecking: false
        });
        
        // Se não há estoque, mostrar aviso crítico
        if (!stock.isInStock) {
          showToast('This item is currently out of stock', 'error');
          setPaymentStep('setup'); // Resetar para não permitir pagamento
        }
        
        return stock.isInStock;
      } else {
        throw new Error(data.error || 'Failed to check stock');
      }
    } catch (error) {
      console.error('❌ Error checking stock:', error);
      showToast('Unable to verify stock availability', 'error');
      setStockStatus(prev => ({ ...prev, isChecking: false }));
      return false;
    }
  };

  const loadPricePreview = async () => {
    try {
      setIsLoadingPreview(true);
      
      // ⚠️ VERIFICAÇÃO CRÍTICA DE ESTOQUE ANTES DE MOSTRAR PREÇOS
      const isInStock = await checkStockAvailability();
      
      if (!isInStock) {
        setIsLoadingPreview(false);
        return; // Não carregar preços se não há estoque
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPricePreview({
        pinPrice: pin.price,
        shipping: 15.00,
        serviceCharge: pin.price * 0.05,
        finalPrice: pin.price + 15.00 + (pin.price * 0.05)
      });
    } catch (error) {
      console.error('Error loading price preview:', error);
      showToast('Failed to load price preview', 'error');
    } finally {
      setIsLoadingPreview(false);
    }
  };

  const createPaymentIntent = async () => {
    try {
      console.log('🔄 Creating payment intent...', {
        amount: pricePreview?.finalPrice,
        pinId: pin.id,
        userId: user?.id,
        selectedAddressId
      });
      
      console.log('🔍 Pin details:', { 
        pinId: pin.id, 
        pinName: pin.name, 
        sellerId: pin.sellerId,
        currentUserId: user!.id,
        isSameUser: pin.sellerId === user!.id
      });
      
      // ⚠️ VERIFICAÇÃO CRÍTICA DE ESTOQUE ANTES DO PAGAMENTO
      console.log('📦 Checking stock before payment...');
      const isInStock = await checkStockAvailability();
      
      if (!isInStock) {
        console.log('❌ Stock check failed - aborting payment');
        showToast('Item is out of stock. Please try again later.', 'error');
        setPaymentStep('setup');
        return;
      }
      
      console.log('✅ Stock check passed - proceeding with payment');
      setIsLoadingPreview(true);
      
      const response = await fetch('/api/shopping/payments/create-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: pricePreview!.finalPrice, // Valor em dólares - backend fará conversão para centavos
          pinId: pin.id,
          userId: user!.id,
          shippingAddressId: selectedAddressId,
          description: `Purchase of pin: ${pin.name}`
        })
      });

      const data = await response.json();
      console.log('📝 Payment intent response:', data);
      
      if (data.success) {
        setClientSecret(data.clientSecret);
        setOrderId(data.orderId);
        setPaymentStep('payment');
        console.log('✅ Payment intent created successfully');
      } else {
        console.error('❌ Payment intent failed:', data.error);
        showToast(data.error || 'Failed to initialize payment', 'error');
        setPaymentStep('setup'); // Reset to setup on error
      }
    } catch (error: any) {
      console.error('❌ Payment intent error:', error);
      showToast('Failed to initialize payment', 'error');
      setPaymentStep('setup'); // Reset to setup on error
    } finally {
      setIsLoadingPreview(false);
    }
  };

  // ✅ CALLBACK PARA PAGAMENTO BEM-SUCEDIDO
  const handlePaymentSuccess = async (paymentIntent: any) => {
    try {
      setPaymentStep('processing');
      
      // Confirmar pagamento no backend
      const response = await fetch('/api/shopping/payments/confirm', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          paymentIntentId: paymentIntent.id,
          orderId: orderId
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setPaymentStep('success');
        showToast('Purchase completed successfully!', 'success');
        onPurchaseSuccess();
        
        // ✅ REDIRECIONAR PARA PÁGINA DE SUCESSO
        // Construir URL com parâmetros necessários
        const successUrl = `/payment/success?payment_intent=${paymentIntent.id}&order_id=${orderId}`;
        
        // Fechar modal e redirecionar
        onClose();
        window.location.href = successUrl;
      } else {
        showToast(data.error || 'Payment confirmation failed', 'error');
        setPaymentStep('payment');
      }
    } catch (error: any) {
      showToast('Payment confirmation failed', 'error');
      setPaymentStep('payment');
    }
  };

  // ✅ CALLBACK PARA ERRO DE PAGAMENTO
  const handlePaymentError = (error: string) => {
    showToast(error, 'error');
    setPaymentStep('payment');
  };

  const handlePurchase = async () => {
    if (!user) {
      showToast('Please sign in to purchase pins', 'error');
      return;
    }

    if (user.id === pin.sellerId) {
      showToast('You cannot purchase your own pin', 'error');
      return;
    }

    try {
      setIsPurchasing(true);
      
      // Simulate purchase
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showToast('Purchase completed successfully!', 'success');
      onPurchaseSuccess();
      onClose();
      
    } catch (error: any) {
      console.error('Purchase error:', error);
      showToast('Failed to complete purchase', 'error');
    } finally {
      setIsPurchasing(false);
    }
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Utility to safely format prices coming as number or string
  const formatPrice = (price: number | string | undefined | null): string => {
    const numeric =
      typeof price === 'number'
        ? price
        : parseFloat(String(price ?? '0'));

    // If the parsed value is not a valid number, fall back to $0.00 to avoid crashes
    if (isNaN(numeric)) {
      return '$0.00';
    }

    return `$${numeric.toFixed(2)}`;
  };

  if (!isOpen) return null;

  return (
    <GlassModal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center justify-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <CreditCardIcon className="w-4 h-4 text-white" />
          </div>
          <span className="text-xl font-semibold text-white">Secure Checkout</span>
        </div>
      }
      className="max-w-xl"
    >
      <div 
        className="space-y-4"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        
        {/* Product Header / Order Summary */}
        <div className="bg-white/5 rounded-lg border border-white/10">
          <button
            onClick={() => toggleSection('orderSummary')}
            className="w-full flex items-center justify-between p-3 text-left"
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={pin.imageUrl}
                  alt={pin.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <h3 className="text-white font-medium">{formatPrice(pin.price)}</h3>
                <p className="text-sm text-gray-400">{pin.sellerName}</p>
              </div>
            </div>
            {expandedSections.orderSummary ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </button>
          
          {expandedSections.orderSummary && (
            <div className="px-3 pb-3 border-t border-white/10 pt-3">
              <h4 className="text-white font-medium mb-3">Order summary</h4>
              <p className="text-sm text-gray-400 mb-4">{pin.sellerName}</p>
              
              {/* Product Details */}
              <div className="flex items-start space-x-3 mb-4">
                <div className="w-16 h-16 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={pin.imageUrl}
                    alt={pin.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h5 className="text-white font-medium">{pin.name}</h5>
                  <div className="flex items-center space-x-2">
                  <p className="text-sm text-gray-400">Qty: 1</p>
                    {/* 📦 INDICADOR DE ESTOQUE */}
                    {stockStatus.isChecking ? (
                      <div className="flex items-center space-x-1">
                        <LoadingSpinner size="sm" />
                        <span className="text-xs text-gray-400">Checking stock...</span>
                      </div>
                    ) : stockStatus.isInStock ? (
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-green-400">
                          {stockStatus.quantity > 1 ? `${stockStatus.quantity} in stock` : 'Last one!'}
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="text-xs text-red-400">Out of stock</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-white font-medium">
                  {formatPrice(pin.price)}
                </div>
              </div>
              
              {/* Price Breakdown */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">Pin Price</span>
                  <span className="text-white">{formatPrice(pin.price)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Shipping</span>
                  <span className="text-white">{pricePreview ? formatPrice(pricePreview.shipping) : formatPrice(15)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">PinPal® Service Charge *</span>
                  <span className="text-white">{pricePreview ? formatPrice(pricePreview.serviceCharge) : formatPrice(pin.price * 0.05)}</span>
                </div>
                <div className="border-t border-white/10 pt-2 mt-2">
                  <div className="flex justify-between font-semibold text-lg">
                    <span className="text-white">Final Price for Buyer</span>
                    <span className="text-white">{pricePreview ? formatPrice(pricePreview.finalPrice) : formatPrice(pin.price + 15 + (pin.price * 0.05))}</span>
                  </div>
                </div>
              </div>
              
              {/* Service Charge Notice */}
              <div className="mt-4 p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                <p className="text-xs text-yellow-200">
                  You will be charged a service fee from Stripe which varies depending upon the payment method you choose.
                </p>
                <p className="text-xs text-yellow-200 mt-1">
                  * PinPal® Service Charge is non-refundable
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Contact Section */}
        <div className="bg-white/5 rounded-lg border border-white/10">
          <button
            onClick={() => toggleSection('contact')}
            className="w-full flex items-center justify-between p-3 text-left"
          >
            <span className="text-white font-medium">Contact</span>
            {expandedSections.contact ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </button>
          
          {!expandedSections.contact && (
            <div className="px-3 pb-3">
              <p className="text-sm text-gray-400">{selectedContact}</p>
            </div>
          )}

          {expandedSections.contact && (
            <div className="px-3 pb-3 space-y-3 border-t border-white/10 pt-3">
              <div>
                <p className="text-sm text-gray-400 mb-2">Email</p>
                {isLoadingEmails ? (
                  <div className="flex justify-center py-4">
                    <LoadingSpinner size="sm" />
                  </div>
                ) : contactEmails.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-gray-400 text-sm mb-3">No email addresses found</p>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => {
                        console.log('🔥 Add Email button clicked!');
                        console.log('🔥 Current showAddEmailModal state:', showAddEmailModal);
                        setShowAddEmailModal(true);
                        console.log('🔥 setShowAddEmailModal(true) called');
                      }}
                      className="inline-flex items-center space-x-2"
                    >
                      <PlusIcon className="w-4 h-4" />
                      <span>Add Email</span>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {contactEmails.map((email) => (
                      <div key={email.id} className="space-y-1">
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="contact"
                            value={email.email}
                            checked={selectedContact === email.email}
                            onChange={(e) => setSelectedContact(e.target.value)}
                            className="text-blue-500"
                          />
                          <div className="flex items-center space-x-2">
                            <span className="text-white text-sm">{email.email}</span>
                            {email.isPrimary && (
                              <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded">
                                Primary
                              </span>
                            )}
                            {email.emailName && (
                              <span className="text-xs text-gray-400">({email.emailName})</span>
                            )}
                          </div>
                        </label>
                        <div className="flex space-x-2 text-xs ml-6">
                          <button 
                            type="button"
                            className="text-blue-400 hover:text-blue-300"
                            onClick={() => handleEditEmail(email.id)}
                          >
                            <PencilIcon className="w-3 h-3 inline mr-1" />
                            Edit
                          </button>
                          <button 
                            type="button"
                            className="text-red-400 hover:text-red-300"
                            onClick={() => handleRemoveEmailClick(email.id)}
                          >
                            <TrashIcon className="w-3 h-3 inline mr-1" />
                            Remove
                          </button>
                        </div>
                      </div>
                    ))}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        console.log('🔥 Add email address button clicked!');
                        console.log('🔥 Current showAddEmailModal state:', showAddEmailModal);
                        setShowAddEmailModal(true);
                        console.log('🔥 setShowAddEmailModal(true) called');
                      }}
                      className="inline-flex items-center space-x-2 text-blue-400 hover:text-blue-300"
                    >
                      <PlusIcon className="w-4 h-4" />
                      <span>Add email address</span>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Ship to Section */}
        <div className="bg-white/5 rounded-lg border border-white/10">
          <button
            onClick={() => toggleSection('shipping')}
            className="w-full flex items-center justify-between p-3 text-left"
          >
            <span className="text-white font-medium">Ship to</span>
            {expandedSections.shipping ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </button>
          
          {!expandedSections.shipping && selectedAddress && (
            <div className="px-3 pb-3">
              <p className="text-sm text-white">{selectedAddress.fullName}</p>
              <p className="text-sm text-gray-400">{selectedAddress.addressLine1}</p>
              <p className="text-sm text-gray-400">{selectedAddress.city}, {selectedAddress.state} {selectedAddress.postalCode}</p>
            </div>
          )}

          {expandedSections.shipping && (
            <div className="px-3 pb-3 space-y-3 border-t border-white/10 pt-3">
              {isLoadingAddresses ? (
                <div className="flex justify-center py-4">
                  <LoadingSpinner size="sm" />
                </div>
              ) : addresses.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-gray-400 text-sm mb-3">No shipping addresses found</p>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setShowAddAddressModal(true)}
                    className="inline-flex items-center space-x-2"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add Address</span>
                  </Button>
                </div>
              ) : (
                <>
                  {addresses.map((address) => (
                    <label key={address.id} className="flex items-start space-x-2">
                      <input
                        type="radio"
                        name="address"
                        checked={selectedAddressId === address.id}
                        onChange={() => setSelectedAddressId(address.id)}
                        className="text-blue-500 mt-1"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <p className="text-white text-sm font-medium">{address.addressName}</p>
                          {address.isDefault && (
                            <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded">
                              Default
                            </span>
                          )}
                        </div>
                        <p className="text-white text-sm">{address.fullName}</p>
                        <p className="text-gray-400 text-sm">{address.addressLine1}</p>
                        {address.addressLine2 && (
                          <p className="text-gray-400 text-sm">{address.addressLine2}</p>
                        )}
                        <p className="text-gray-400 text-sm">
                          {address.city}, {address.state} {address.postalCode}
                        </p>
                        {address.phone && (
                          <p className="text-gray-400 text-sm">{address.phone}</p>
                        )}
                        <div className="flex space-x-2 text-xs mt-1">
                          {!address.isDefault && (
                            <button 
                              type="button"
                              className="text-green-400 hover:text-green-300"
                              onClick={() => handleSetAsDefault(address.id)}
                            >
                              Set as Default
                            </button>
                          )}
                          <button 
                            type="button"
                            className="text-blue-400 hover:text-blue-300"
                            onClick={() => handleEditAddress(address.id)}
                          >
                            <PencilIcon className="w-3 h-3 inline mr-1" />
                            Edit
                          </button>
                          <button 
                            type="button"
                            className="text-red-400 hover:text-red-300"
                            onClick={() => handleDeleteAddress(address.id)}
                          >
                            <TrashIcon className="w-3 h-3 inline mr-1" />
                            Remove
                          </button>
                        </div>
                      </div>
                    </label>
                  ))}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAddAddressModal(true)}
                    className="inline-flex items-center space-x-2 text-blue-400 hover:text-blue-300"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add shipping address</span>
                  </Button>
                </>
              )}
            </div>
          )}
        </div>

        {/* Delivery Section */}
        <div className="bg-white/5 rounded-lg border border-white/10">
          <button
            onClick={() => toggleSection('delivery')}
            className="w-full flex items-center justify-between p-3 text-left"
          >
            <span className="text-white font-medium">Delivery</span>
            {expandedSections.delivery ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </button>
          
          {!expandedSections.delivery && (
            <div className="px-3 pb-3">
              <p className="text-sm text-white">Estimated arrival Jun 30 • $15.00</p>
              <p className="text-sm text-gray-400">Standard</p>
            </div>
          )}

          {expandedSections.delivery && (
            <div className="px-3 pb-3 border-t border-white/10 pt-3">
              <div className="space-y-2">
                <label className="flex items-center justify-between p-2 border border-white/10 rounded">
                  <div className="flex items-center space-x-2">
                    <input type="radio" name="delivery" checked className="text-blue-500" />
                    <div>
                      <p className="text-white text-sm">Standard Shipping</p>
                      <p className="text-gray-400 text-xs">5-7 business days</p>
                    </div>
                  </div>
                  <span className="text-white text-sm">$15.00</span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Pay with Section */}
        <div className="bg-white/5 rounded-lg border border-white/10">
          <button
            onClick={() => toggleSection('payment')}
            className="w-full flex items-center justify-between p-3 text-left"
          >
            <div className="flex items-center space-x-2">
              <span className="text-white font-medium">Pay with</span>
              <ShieldCheckIcon className="w-4 h-4 text-gray-400" />
            </div>
            {expandedSections.payment ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-400" />
            )}
          </button>
          
          {!expandedSections.payment && (
            <div className="px-3 pb-3">
              <p className="text-sm text-blue-400">Add payment info</p>
            </div>
          )}

          {expandedSections.payment && (
            <div className="px-3 pb-3 border-t border-white/10 pt-3">
              <div className="space-y-3">
                {/* ✅ STRIPE ELEMENTS SEGURO - IMPLEMENTAÇÃO REAL */}
                {paymentStep === 'setup' && (
                  <div className="text-center py-8">
                    <LoadingSpinner size="lg" />
                    <p className="text-gray-400 mt-3">Setting up secure payment...</p>
                  </div>
                )}

                {paymentStep === 'payment' && clientSecret && (
                  <StripePaymentElement
                    clientSecret={clientSecret}
                    amount={pricePreview?.finalPrice || 0}
                    onPaymentSuccess={handlePaymentSuccess}
                    onPaymentError={handlePaymentError}
                  />
                )}

                {paymentStep === 'processing' && (
                  <div className="text-center py-8">
                    <LoadingSpinner size="lg" />
                    <p className="text-white font-medium mt-3">Processing Payment...</p>
                    <p className="text-gray-400 text-sm mt-1">Please wait while we confirm your payment</p>
                  </div>
                )}

                {paymentStep === 'success' && (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <p className="text-white font-medium">Payment Successful!</p>
                    <p className="text-gray-400 text-sm mt-1">Your order has been confirmed</p>
                  </div>
                )}

                {/* Billing Address (only show during payment step) */}
                {paymentStep === 'payment' && (
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium text-white">Billing address</h3>
                    
                    {(() => {
                      console.log('🔍 Rendering billing section - billingAddresses.length:', billingAddresses.length);
                      console.log('🔍 Current billingAddresses:', billingAddresses);
                      console.log('🔍 Current selectedBillingId:', selectedBillingId);
                      return billingAddresses.length === 0;
                    })() ? (
                      <>
                        {/* Same as shipping address option (default) */}
                        {selectedAddress && (
                          <div 
                            className={`p-3 rounded-lg border cursor-pointer transition-all ${
                              selectedBillingId === 'same-as-shipping' || selectedBillingId === ''
                                ? 'border-blue-500 bg-blue-500/10'
                                : 'border-white/10 bg-white/5 hover:border-white/20'
                            }`}
                            onClick={() => setSelectedBillingId('same-as-shipping')}
                          >
                            <label className="flex items-start space-x-3 cursor-pointer">
                              <input
                                type="radio"
                                name="billingAddress"
                                checked={selectedBillingId === 'same-as-shipping' || selectedBillingId === ''}
                                onChange={() => setSelectedBillingId('same-as-shipping')}
                                className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500 mt-0.5"
                              />
                              <div>
                                <p className="text-white text-sm font-medium">Same as shipping address</p>
                                <p className="text-gray-400 text-xs">{selectedAddress.fullName}</p>
                                <p className="text-gray-400 text-xs">{selectedAddress.addressLine1}</p>
                                <p className="text-gray-400 text-xs">{selectedAddress.city}, {selectedAddress.state} {selectedAddress.postalCode}</p>
                              </div>
                            </label>
                          </div>
                        )}
                        
                        {/* Add billing address link */}
                        <button 
                          type="button"
                          onClick={() => setShowAddBillingModal(true)}
                          className="text-blue-400 text-sm hover:text-blue-300 transition-colors"
                        >
                          + Add different billing address
                        </button>
                      </>
                    ) : (
                      <>
                        {/* Show added billing addresses */}
                        <div className="space-y-2">
                          {billingAddresses.map((address) => (
                            <div 
                              key={address.id}
                              className={`p-3 rounded-lg border cursor-pointer transition-all ${
                                selectedBillingId === address.id
                                  ? 'border-blue-500 bg-blue-500/10'
                                  : 'border-white/10 bg-white/5 hover:border-white/20'
                              }`}
                              onClick={() => setSelectedBillingId(address.id)}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex items-start space-x-3 flex-1">
                                  <input
                                    type="radio"
                                    name="billingAddress"
                                    checked={selectedBillingId === address.id}
                                    onChange={() => setSelectedBillingId(address.id)}
                                    className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500 mt-0.5"
                                  />
                                  <div>
                                    <p className="text-white text-sm font-medium">{address.fullName}</p>
                                    <p className="text-gray-400 text-xs">{address.addressLine1}</p>
                                    {address.addressLine2 && (
                                      <p className="text-gray-400 text-xs">{address.addressLine2}</p>
                                    )}
                                    <p className="text-gray-400 text-xs">
                                      {address.city}, {address.state} {address.postalCode}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex space-x-2 ml-4">
                                  <button
                                    type="button"
                                    className="text-gray-400 hover:text-white text-xs"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // TODO: Implement edit billing address
                                    }}
                                  >
                                    Edit
                                  </button>
                                  <button
                                    type="button"
                                    className="text-red-400 hover:text-red-300 text-xs"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setBillingAddresses(prev => 
                                        prev.filter(addr => addr.id !== address.id)
                                      );
                                      if (selectedBillingId === address.id) {
                                        setSelectedBillingId('same-as-shipping');
                                      }
                                    }}
                                  >
                                    Remove
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Same as shipping address option */}
                        {selectedAddress && (
                          <div 
                            className={`p-3 rounded-lg border cursor-pointer transition-all ${
                              selectedBillingId === 'same-as-shipping'
                                ? 'border-blue-500 bg-blue-500/10'
                                : 'border-white/10 bg-white/5 hover:border-white/20'
                            }`}
                            onClick={() => setSelectedBillingId('same-as-shipping')}
                          >
                            <label className="flex items-start space-x-3 cursor-pointer">
                              <input
                                type="radio"
                                name="billingAddress"
                                checked={selectedBillingId === 'same-as-shipping'}
                                onChange={() => setSelectedBillingId('same-as-shipping')}
                                className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500 mt-0.5"
                              />
                              <div>
                                <p className="text-white text-sm font-medium">Same as shipping address</p>
                                <p className="text-gray-400 text-xs">{selectedAddress.fullName}</p>
                                <p className="text-gray-400 text-xs">{selectedAddress.addressLine1}</p>
                                <p className="text-gray-400 text-xs">{selectedAddress.city}, {selectedAddress.state} {selectedAddress.postalCode}</p>
                              </div>
                            </label>
                          </div>
                        )}
                        
                        {/* Add another billing address */}
                        <button 
                          type="button"
                          onClick={() => setShowAddBillingModal(true)}
                          className="w-full p-2 border border-dashed border-white/20 rounded-lg text-blue-400 hover:text-blue-300 hover:border-blue-400/30 transition-all text-xs"
                        >
                          + Add another billing address
                        </button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>


      </div>

      {/* Add Email Modal */}
      <AddEmailModal
        isOpen={showAddEmailModal}
        onClose={() => {
          console.log('🔥 AddEmailModal onClose called');
          setShowAddEmailModal(false);
        }}
        onEmailAdded={handleAddEmail}
        title="Add email"
        placeholder="Email"
        showPrivacyNotice={true}
      />

      {/* Add Shipping Address Modal */}
      <AddShippingAddressModal
        isOpen={showAddAddressModal}
        onClose={() => setShowAddAddressModal(false)}
        onAddressAdded={handleAddAddress}
        userId={user?.id || ''}
        addAddressFunction={addAddress}
      />

      {/* Edit Shipping Address Modal */}
      <EditShippingAddressModal
        isOpen={showEditAddressModal}
        onClose={() => setShowEditAddressModal(false)}
        onAddressUpdated={handleAddressUpdated}
        userId={user?.id || ''}
        address={editingAddress}
        updateAddressFunction={updateAddress}
      />

      {/* Edit Email Modal */}
      <EditEmailModal
        isOpen={showEditEmailModal}
        onClose={() => setShowEditEmailModal(false)}
        onEmailUpdated={handleEmailUpdated}
        userId={user?.id || ''}
        email={editingEmail}
        updateEmailFunction={updateEmail}
      />

      {/* Confirm Remove Email Modal */}
      <ConfirmRemoveModal
        isOpen={confirmRemoveEmail.isOpen}
        onConfirm={handleRemoveEmail}
        onCancel={() => setConfirmRemoveEmail({
          isOpen: false,
          emailId: '',
          emailAddress: '',
          emailName: undefined
        })}
        type="email"
        itemName={confirmRemoveEmail.emailName || confirmRemoveEmail.emailAddress}
        itemDetails={confirmRemoveEmail.emailName ? confirmRemoveEmail.emailAddress : undefined}
      />

      {/* Confirm Remove Address Modal */}
      <ConfirmRemoveModal
        isOpen={confirmRemoveAddress.isOpen}
        onConfirm={handleConfirmDeleteAddress}
        onCancel={() => setConfirmRemoveAddress({
          isOpen: false,
          addressId: '',
          addressName: '',
          fullAddress: ''
        })}
        type="address"
        itemName={confirmRemoveAddress.addressName}
        itemDetails={confirmRemoveAddress.fullAddress}
      />

      {/* Add Billing Address Modal */}
      <AddBillingAddressModal
        isOpen={showAddBillingModal}
        onClose={() => setShowAddBillingModal(false)}
        onBillingAddressAdded={handleAddBillingAddress}
        userId={user?.id || ''}
      />
    </GlassModal>
  );
}; 