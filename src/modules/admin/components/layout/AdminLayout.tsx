import React, { useState, useRef, useEffect } from 'react';
import { Outlet, useNavigate, useLocation, Link } from 'react-router-dom';
import { 
  Bars3Icon, 
  XMarkIcon,
  MapPinIcon,
  HomeIcon,
  UsersIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowLeftOnRectangleIcon,
  WrenchScrewdriverIcon,
  UserIcon,
  ShieldCheckIcon,
  FlagIcon,
  BeakerIcon,
  SignalIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import { useTheme } from '@/theme/ThemeProvider';
import { useAuthStore } from '@/store/authStore';
import { Avatar } from '@/components/ui/Avatar';
import { userNameUtils } from '@/utils/nameUtils';
import { profileUtils } from '@/utils/profileUtils';
import { darkModeClasses } from '@/theme/darkModeClasses';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  current?: boolean;
}

const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/admin', icon: HomeIcon },
  { name: 'Trading Points', href: '/admin/trading-points', icon: MapPinIcon },
  { name: 'Users', href: '/admin/users', icon: UsersIcon },
  { name: 'Push Notifications', href: '/admin/push-notifications', icon: BellIcon },
  { name: 'Online Users', href: '/admin/online-users', icon: SignalIcon },
  { name: 'Pin Reports', href: '/admin/pin-reports', icon: FlagIcon },
  { name: 'Firebase Auth', href: '/admin/auth', icon: ShieldCheckIcon },
  { name: 'System Metrics', href: '/admin/metrics', icon: ChartBarIcon },
  { name: 'Analytics', href: '/admin/analytics', icon: ChartBarIcon },
  { name: 'Settings', href: '/admin/settings', icon: Cog6ToothIcon },
  ...(import.meta.env.DEV ? [{ name: 'Dev Tools', href: '/admin/dev-tools', icon: WrenchScrewdriverIcon }] : []),
  ...(import.meta.env.DEV ? [{ name: 'BG Removal Test', href: '/admin/test-bg-removal', icon: BeakerIcon }] : []),
];

export const AdminLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { actualTheme, toggleTheme } = useTheme();
  const { user, logout } = useAuthStore();
  const menuRef = useRef<HTMLDivElement>(null);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-white dark:bg-black">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600/75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex w-64 flex-col h-full card-primary shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">PinPal Admin</h1>
            <button
              type="button"
              className="text-gray-500 hover:text-gray-600 dark:text-gray-500 dark:text-gray-400 dark:hover:text-gray-600 dark:text-gray-300"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.href !== '/admin' && location.pathname.startsWith(item.href));
              return (
                <button
                  key={item.name}
                  onClick={() => {
                    navigate(item.href);
                    setSidebarOpen(false);
                  }}
                  className={`group flex w-full items-center rounded-md px-2 py-2 text-sm font-medium ${
                    isActive
                      ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-600 dark:text-gray-300 dark:hover:bg-gray-100 dark:bg-gray-700 dark:hover:text-gray-900 dark:text-white'
                  }`}
                >
                  <item.icon className="mr-3 h-6 w-6 flex-shrink-0" />
                  {item.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col h-full bg-white dark:bg-gray-900 shadow-sm border-r border-gray-200 dark:border-gray-700">
          <div className="flex h-16 items-center px-4 border-b border-gray-200 dark:border-gray-700">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">PinPal Admin</h1>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.href !== '/admin' && location.pathname.startsWith(item.href));
              return (
                <button
                  key={item.name}
                  onClick={() => navigate(item.href)}
                  className={`group flex w-full items-center rounded-md px-2 py-2 text-sm font-medium transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-600 dark:text-gray-300 dark:hover:bg-gray-100 dark:bg-gray-700 dark:hover:text-gray-900 dark:text-white'
                  }`}
                >
                  <item.icon className="mr-3 h-6 w-6 flex-shrink-0" />
                  {item.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="flex h-16 items-center px-4 sm:px-6 lg:px-8">
            <button
              type="button"
              className="text-gray-500 hover:text-gray-600 dark:text-gray-500 dark:text-gray-400 dark:hover:text-gray-600 dark:text-gray-300 lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            
            {/* Spacer to push content to the right */}
            <div className="flex-1"></div>
            
            <div className="flex items-center gap-4">
              <button
                onClick={toggleTheme}
                className="p-2 text-gray-500 hover:text-gray-600 dark:text-gray-500 dark:text-gray-400 dark:hover:text-gray-600 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:bg-gray-700"
                title="Toggle theme"
              >
                {actualTheme === 'dark' ? (
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                )}
              </button>
              
              {/* User Menu */}
              {user && (
                <div className="relative" ref={menuRef}>
                  <button
                    className="flex items-center text-sm text-gray-700 dark:text-gray-200 focus:outline-none rounded-xl hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 px-3 py-2 transition-colors"
                    onClick={() => setMenuOpen((open) => !open)}
                    aria-haspopup="true"
                    aria-expanded={menuOpen}
                  >
                    <Avatar firstName={user.firstName} lastName={user.lastName} src={user.avatarUrl} size={32} className="mr-2" />
                    <span className="font-semibold hidden sm:inline-block">{userNameUtils.getFullName(user) || 'User'}</span>
                    <svg className="w-4 h-4 ml-1 text-gray-500 dark:text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                  </button>
                  {menuOpen && (
                    <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-900 border border-gray-200 dark:border-custom-gray-800 rounded-xl shadow-lg z-50 animate-fade-in">
                      {/* User Info Section */}
                      <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-200 dark:border-gray-700">
                        <div className="flex items-center space-x-3 mb-3">
                          <Avatar firstName={user.firstName} lastName={user.lastName} src={user?.avatarUrl} size={48} />
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white">{userNameUtils.getFullName(user)}</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{user?.email}</p>
                          </div>
                        </div>
                      </div>

                      {/* Menu Items */}
                      <ul className="py-1">
                        <li>
                          <Link to={profileUtils.getOwnProfileUrl(user)} className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 transition-colors">
                            <UserIcon className="w-5 h-5 mr-3" />
                            Profile
                          </Link>
                        </li>
                        <li>
                          <Link to="/settings" className="flex items-center px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 transition-colors">
                            <Cog6ToothIcon className="w-5 h-5 mr-3" />
                            Settings
                          </Link>
                        </li>
                        {/* Platform Link - Go back to main platform */}
                        <li>
                          <Link to="/" className="flex items-center px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors">
                            <ShieldCheckIcon className="w-5 h-5 mr-3" />
                            Platform
                          </Link>
                        </li>
                        <li className="border-t border-gray-100 dark:border-gray-200 dark:border-gray-700">
                          <button
                            onClick={async () => await handleLogout()}
                            className="flex items-center w-full px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-100 dark:bg-gray-700 transition-colors"
                          >
                            <ArrowLeftOnRectangleIcon className="w-5 h-5 mr-3" />
                            Logout
                          </button>
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-4 sm:p-6 lg:p-8 max-w-page mx-auto">
          <Outlet />
        </main>
      </div>
    </div>
  );
}; 