import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { CleanPageLayout, CleanGrid } from '@/components/ui/design-system/Layout';
import { useAuthStore } from '@/store/authStore';
import { useUserBoards, useCreateBoard, useUpdateBoard } from '@/hooks/api/useApiQueries';
import { BoardCard } from '@/components/ui/BoardCard';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { NewBoardModal } from './components/NewBoardModal';
import { EditBoardModal } from './components/EditBoardModal';
import { DraggableBoardGrid } from './components/DraggableBoardGrid';
import { ShareBoardModal } from './components/ShareBoardModal';
import { ConfirmDeleteBoardModal } from '@/components/ui/ConfirmDeleteBoardModal';
import { Button } from '@/components/ui/design-system/Button';
import { Input } from '@/components/ui/design-system/Input';
import { PlusIcon, Squares2X2Icon, MagnifyingGlassIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

type SortOption = 'custom' | 'name-asc' | 'name-desc' | 'updated-desc' | 'updated-asc';

export const MyPinsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [showNewBoardModal, setShowNewBoardModal] = useState(false);
  const [editingBoard, setEditingBoard] = useState<any>(null);
  const [orderedBoards, setOrderedBoards] = useState<any[]>([]);
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareBoard, setShareBoard] = useState<any>(null);
  const [deletingBoard, setDeletingBoard] = useState<any>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  // New states for search and sorting
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('custom');
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  
  // Carregar boards do usuário
  const { data: boards = [], isLoading, error } = useUserBoards(user?.id, true); // includePrivate = true
  
  // Ensure user has default boards if they don't have any
  React.useEffect(() => {
    const ensureDefaultBoards = async () => {
      if (user?.id && boards.length === 0 && !isLoading && !error) {
        console.log('🔍 User has no boards, checking if we should create default boards...');
        try {
          const { boardsService } = await import('@/services/boardsService');
          await boardsService.ensureDefaultBoards(user.id);
        } catch (error) {
          console.error('❌ Failed to ensure default boards:', error);
        }
      }
    };

    ensureDefaultBoards();
  }, [user?.id, boards.length, isLoading, error]);
  
  // Update ordered boards when boards data changes
  React.useEffect(() => {
    if (!boards || boards.length === 0) {
      if (orderedBoards.length > 0) {
        setOrderedBoards([]);
      }
      return;
    }

    // Load saved order from localStorage or use default order
    const savedOrder = localStorage.getItem(`user-${user?.id}-board-order`);
    if (savedOrder) {
      try {
        const orderIds = JSON.parse(savedOrder);
        const orderedBoardsList = orderIds
          .map((id: string) => boards.find((board: any) => board.id === id))
          .filter(Boolean)
          .concat(boards.filter((board: any) => !orderIds.includes(board.id)));
        setOrderedBoards(orderedBoardsList);
      } catch {
        setOrderedBoards(boards);
      }
    } else {
      setOrderedBoards(boards);
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 MyPinsPage - Boards updated:', {
        count: boards.length,
        userId: user?.id,
        firstBoard: boards[0]?.name || 'None'
      });
    }
  }, [boards, user?.id]);
  
  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showSortDropdown) {
        const target = event.target as Element;
        if (!target.closest('[data-dropdown="sort-filter"]')) {
          setShowSortDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showSortDropdown]);
  
  // Debug: Log quando componente re-renderiza
  console.log('🔄 MyPinsPage - Component re-render, boards count:', boards?.length || 0);
  
  // Filter and sort boards based on search and sort criteria
  const filteredAndSortedBoards = useMemo(() => {
    let filtered = orderedBoards;
    
    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = orderedBoards.filter(board => 
        board.name?.toLowerCase().includes(query) ||
        board.description?.toLowerCase().includes(query)
      );
    }
    
    // Sort boards
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'custom':
          // Maintain the original order from orderedBoards (user's custom drag order)
          const indexA = orderedBoards.findIndex(board => board.id === a.id);
          const indexB = orderedBoards.findIndex(board => board.id === b.id);
          return indexA - indexB;
        case 'name-asc':
          return (a.name || '').localeCompare(b.name || '');
        case 'name-desc':
          return (b.name || '').localeCompare(a.name || '');
        case 'updated-asc':
          return new Date(a.lastUpdated || a.updatedAt || a.createdAt).getTime() - new Date(b.lastUpdated || b.updatedAt || b.createdAt).getTime();
        case 'updated-desc':
        default:
          return new Date(b.lastUpdated || b.updatedAt || b.createdAt).getTime() - new Date(a.lastUpdated || a.updatedAt || a.createdAt).getTime();
      }
    });
    
    return sorted;
  }, [orderedBoards, searchQuery, sortBy]);
  
  // Sort options for dropdown
  const sortOptions = [
    { value: 'custom', label: 'Custom Order' },
    { value: 'updated-desc', label: 'Recently Updated' },
    { value: 'updated-asc', label: 'Oldest Updated' },
    { value: 'name-asc', label: 'Name (A-Z)' },
    { value: 'name-desc', label: 'Name (Z-A)' },
  ];
  
  // Hooks para criar e atualizar board
  const createBoardMutation = useCreateBoard();
  const updateBoardMutation = useUpdateBoard();

  // ✅ CORREÇÃO: Usar logger condicional
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 MyPinsPage Debug:', {
      userId: user?.id,
      boardsCount: boards?.length || 0,
      isLoading,
      hasError: !!error
    });
  }

  const handleBoardClick = (boardId: string) => {
    navigate(`/boards/${boardId}`);
  };

  const handleBoardEdit = (boardId: string) => {
    const board = boards.find((b: any) => b.id === boardId);
    if (board) {
      setEditingBoard(board);
    }
  };

  const handleUpdateBoard = async (boardId: string, boardData: { name: string; description: string; coverImageUrl: string; isPrivate: boolean }) => {
    console.log('🔄 MyPinsPage - handleUpdateBoard called:', { boardId, boardData });
    try {
      const result = await updateBoardMutation.mutateAsync({
        boardId,
        data: {
          name: boardData.name,
          description: boardData.description,
          coverImage: boardData.coverImageUrl,
          isPrivate: boardData.isPrivate
        }
      });
      console.log('✅ MyPinsPage - Board update completed:', result);
      setEditingBoard(null);
    } catch (error) {
      console.error('❌ MyPinsPage - Error updating board:', error);
      // O erro já é tratado pelo hook useUpdateBoard
    }
  };

  const handleBoardDelete = (boardId: string) => {
    console.log('🔥 handleBoardDelete called with boardId:', boardId);
    const board = boards.find((b: any) => b.id === boardId);
    console.log('🔍 Found board:', board);
    if (!board) {
      console.log('❌ Board not found!');
      return;
    }

    // Verificar se o board contém pins
    const pinCount = board.pinCount || board.pinsCount || 0;
    console.log('📊 Pin count:', pinCount);
    
    if (pinCount > 0) {
      console.log('⚠️ Board has pins, showing error toast');
      toast.error(`Cannot delete board "${board.name}" because it contains ${pinCount} pin${pinCount > 1 ? 's' : ''}. To delete this board, first remove all pins.`);
      return;
    }

    // Se não tem pins, mostrar modal de confirmação
    console.log('✅ Board is empty, showing confirmation modal');
    setDeletingBoard(board);
    setShowDeleteModal(true);
  };

  const confirmBoardDelete = async () => {
    if (!deletingBoard) return;
    
    try {
      // Call delete API directly
      const response = await fetch(`http://localhost:3001/api/boards/${deletingBoard.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete board');
      }

      // Remove from local state immediately
      const updatedBoards = boards.filter((b: any) => b.id !== deletingBoard.id);
      setOrderedBoards(updatedBoards);

      // Update localStorage order
      if (user?.id) {
        const orderIds = updatedBoards.map((b: any) => b.id);
        localStorage.setItem(`user-${user.id}-board-order`, JSON.stringify(orderIds));
      }

      toast.success(`"${deletingBoard.name}" deleted successfully!`);
      
      // Close modal and reset state
      setShowDeleteModal(false);
      setDeletingBoard(null);
      
    } catch (error) {
      console.error('Error deleting board:', error);
      toast.error('Failed to delete board. Please try again.');
    }
  };

  const cancelBoardDelete = () => {
    setShowDeleteModal(false);
    setDeletingBoard(null);
  };

  const handleBoardShare = (boardId: string) => {
    const board = boards.find((b: any) => b.id === boardId);
    if (board) {
      // Set the board to share and open the share modal
      setShareBoard({
        id: board.id,
        name: board.name,
        description: board.description || '',
        isPrivate: board.isPrivate,
        pinsCount: board.pinCount || 0
      });
      setShowShareModal(true);
    }
  };

  const handleCreateBoard = async (boardData: { name: string; description: string; coverImageUrl: string; isPrivate: boolean }) => {
    if (!user) return;
    
    try {
      await createBoardMutation.mutateAsync({
        userId: user.id,
        name: boardData.name,
        description: boardData.description,
        coverImage: boardData.coverImageUrl,
        isPrivate: boardData.isPrivate
      });
      setShowNewBoardModal(false);
    } catch (error) {
      console.error('Error creating board:', error);
      // O erro já é tratado pelo hook useCreateBoard
    }
  };

  const handleBoardReorder = async (newOrderedBoards: any[]) => {
    // Only allow reordering when in custom order mode and no search filter is applied
    if (sortBy !== 'custom') {
      toast.error('Reordering is only available in Custom Order mode');
      return;
    }
    
    if (searchQuery.trim()) {
      toast.error('Please clear search filter before reordering boards');
      return;
    }
    
    setOrderedBoards(newOrderedBoards);
    
    // Save order to localStorage
    const orderIds = newOrderedBoards.map(board => board.id);
    localStorage.setItem(`user-${user?.id}-board-order`, JSON.stringify(orderIds));
    
    // Save order to database
    if (user?.id) {
      try {
        const response = await fetch(`http://localhost:3001/api/boards/user/${user.id}/order`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ boardIds: orderIds }),
        });

        if (!response.ok) {
          throw new Error('Failed to save board order');
        }

        console.log('✅ Board order saved to database:', orderIds);
        toast.success('Board order updated!');
      } catch (error) {
        console.error('❌ Error saving board order:', error);
        toast.error('Failed to save board order to database');
      }
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <CleanPageLayout
        title="My Pinboards"
      >
        <div className="flex justify-center items-center py-16">
          <LoadingSpinner size="lg" />
        </div>
      </CleanPageLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CleanPageLayout
        title="My Pinboards"
      >
        <div className="text-center py-16">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <Squares2X2Icon className="w-8 h-8 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Error loading boards
          </h2>
          <p className="text-gray-400 mb-6 max-w-md mx-auto">
            {error.message || 'Failed to load your boards. Please try again.'}
          </p>
          <Button
            variant="primary"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </CleanPageLayout>
    );
  }

  // Empty state
  if (boards.length === 0) {
    return (
      <>
      <CleanPageLayout
        title="My Pinboards"
        variant="default"
      >
        <div className="text-center py-16">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-800 rounded-full flex items-center justify-center">
            <Squares2X2Icon className="w-8 h-8 text-gray-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Create your first board
          </h2>
          <p className="text-gray-400 mb-6 max-w-md mx-auto">
            Boards help you organize your pins by theme, location, or any way you like.
          </p>
          <Button
            variant="primary"
            size="lg"
            onClick={() => setShowNewBoardModal(true)}
            leftIcon={<PlusIcon className="w-5 h-5" />}
          >
            Create Board
          </Button>
          <p className="text-sm text-gray-500 mt-4">
            You can also create boards while saving pins
          </p>
        </div>
      </CleanPageLayout>
        
        {/* New Board Modal */}
        {showNewBoardModal && (
          <NewBoardModal
            onClose={() => setShowNewBoardModal(false)}
            onSubmit={handleCreateBoard}
          />
        )}

        {/* Edit Board Modal */}
        {editingBoard && (
          <EditBoardModal
            board={{
              id: editingBoard.id,
              name: editingBoard.name,
              description: editingBoard.description || '',
              coverImage: editingBoard.coverImage || '',
              isPrivate: editingBoard.isPrivate
            }}
            onClose={() => setEditingBoard(null)}
            onUpdateBoard={handleUpdateBoard}
          />
        )}

        {/* Share Board Modal */}
        {shareBoard && (
          <ShareBoardModal
            board={shareBoard}
            isOpen={showShareModal}
            onClose={() => {
              setShowShareModal(false);
              setShareBoard(null);
            }}
          />
        )}
      </>
    );
  }

  // Boards list
  return (
    <>
    <CleanPageLayout
      title="My Pinboards"
    >
      {/* Controls Row - Similar to PinBoardViewPage */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        {/* Left side: Search and Sort */}
        <div className="flex flex-col sm:flex-row gap-3 flex-1">
          {/* Search Input */}
          <div className="flex-1 max-w-md">
            <Input
              placeholder="Search boards..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<MagnifyingGlassIcon className="w-4 h-4" />}
              variant="default"
              className="h-10"
              style={{
                backgroundColor: 'white',
                borderColor: '#e5e7eb',
                color: '#111827'
              }}
              className="dark:!bg-gray-800 dark:!border-gray-600 dark:!text-white"
            />
          </div>
          
          {/* Sort Dropdown */}
          <div className="relative" data-dropdown="sort-filter">
            <Button
              variant="secondary"
              onClick={() => setShowSortDropdown(!showSortDropdown)}
              rightIcon={<ChevronDownIcon className="w-4 h-4" />}
              className="h-10 min-w-[160px] justify-between"
            >
              {sortOptions.find(opt => opt.value === sortBy)?.label}
            </Button>
            
            {showSortDropdown && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-[60]">
                {sortOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      setSortBy(option.value as SortOption);
                      setShowSortDropdown(false);
                    }}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-md last:rounded-b-md ${
                      sortBy === option.value 
                        ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                        : 'text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Right side: New Board Button */}
        <div className="flex-shrink-0">
          <Button 
            variant="primary"
            onClick={() => setShowNewBoardModal(true)}
            leftIcon={<PlusIcon className="w-4 h-4" />}
            className="h-10"
          >
            New Board
          </Button>
        </div>
      </div>

      {/* Results Info */}
      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
        <span>
          {searchQuery.trim() 
            ? `${filteredAndSortedBoards.length} of ${orderedBoards.length} boards` 
            : `${orderedBoards.length} boards`
          }
        </span>
        {searchQuery.trim() && (
          <>
            <span>•</span>
            <span>Searching for "{searchQuery}"</span>
            <span>•</span>
            <span className="text-amber-400">Drag to reorder disabled during search</span>
          </>
        )}
        {!searchQuery.trim() && sortBy === 'custom' && (
          <>
            <span>•</span>
            <span className="text-blue-400">Custom order - drag to reorder</span>
          </>
        )}
        {!searchQuery.trim() && sortBy !== 'custom' && (
          <>
            <span>•</span>
            <span>Sorted by {sortOptions.find(opt => opt.value === sortBy)?.label}</span>
          </>
        )}
      </div>

      {/* Boards Grid */}
      {filteredAndSortedBoards.length === 0 && searchQuery.trim() ? (
        <div className="text-center py-12">
          <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <MagnifyingGlassIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No boards found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            No boards match your search for "{searchQuery}"
          </p>
          <Button
            variant="secondary"
            onClick={() => setSearchQuery('')}
          >
            Clear Search
          </Button>
        </div>
      ) : (
        <DraggableBoardGrid
          boards={filteredAndSortedBoards}
          onReorder={handleBoardReorder}
          onBoardClick={handleBoardClick}
          onBoardEdit={handleBoardEdit}
          onBoardDelete={handleBoardDelete}
          onBoardShare={handleBoardShare}
          isOwner={true}
          isDragDisabled={sortBy !== 'custom' || searchQuery.trim() !== ''}
        />
      )}
    </CleanPageLayout>
      
      {/* New Board Modal */}
      {showNewBoardModal && (
        <NewBoardModal
          onClose={() => setShowNewBoardModal(false)}
          onSubmit={handleCreateBoard}
        />
      )}

      {/* Edit Board Modal */}
      {editingBoard && (
        <EditBoardModal
          board={{
            id: editingBoard.id,
            name: editingBoard.name,
            description: editingBoard.description || '',
            coverImage: editingBoard.coverImage || '',
            isPrivate: editingBoard.isPrivate
          }}
          onClose={() => setEditingBoard(null)}
          onUpdateBoard={handleUpdateBoard}
        />
      )}

      {/* Share Board Modal */}
      {shareBoard && (
        <ShareBoardModal
          board={shareBoard}
          isOpen={showShareModal}
          onClose={() => {
            setShowShareModal(false);
            setShareBoard(null);
          }}
        />
      )}

      {/* Delete Board Confirmation Modal */}
      {deletingBoard && (
        <ConfirmDeleteBoardModal
          isOpen={showDeleteModal}
          onConfirm={confirmBoardDelete}
          onCancel={cancelBoardDelete}
          boardName={deletingBoard.name}
          pinCount={deletingBoard.pinCount || deletingBoard.pinsCount || 0}
        />
      )}
    </>
  );
};

// ✅ CORREÇÃO: Adicionar export default para importação dinâmica
export default MyPinsPage; 