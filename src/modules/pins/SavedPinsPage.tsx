import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { BookmarkIcon, MagnifyingGlassIcon, ArrowPathIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';

import { PinCard } from '@/components/ui/PinCard';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Input } from '@/components/ui/design-system/Input';
import { Button } from '@/components/ui/design-system/Button';
import { PageToolbar } from '@/components/ui/PageToolbar';
import { ButtonGroup } from '@/components/ui/design-system/patterns/ButtonGroup';
import { PinDetailModal } from '@/components/ui/PinDetailModal/PinDetailModal';
import { CleanPageLayout } from '@/components/layout/CleanPageLayout';
import { useAuthStore } from '@/store/authStore';
import { pinsService } from '@/services/pinsService';
import { Pin } from '@/types/pin';
import { useToast } from '@/hooks/useToast';

export const SavedPinsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { showToast } = useToast();
  
  const [savedPins, setSavedPins] = useState<Pin[]>([]);
  const [filteredPins, setFilteredPins] = useState<Pin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [tradableFilter, setTradableFilter] = useState<'all' | 'display' | 'trade' | 'sale'>('all');
  const [showTradableDropdown, setShowTradableDropdown] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>(() => {
    const saved = localStorage.getItem('saved-pins-view-mode');
    return (saved as 'grid' | 'masonry') || 'masonry';
  });
  const [selectedPin, setSelectedPin] = useState<Pin | null>(null);

  // Load saved pins
  useEffect(() => {
    loadSavedPins();
  }, []);

  // Filter pins based on search and tradable status
  useEffect(() => {
    let filtered = savedPins;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(pin =>
        pin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pin.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pin.origin?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pin.series?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply tradable filter
    if (tradableFilter !== 'all') {
      filtered = filtered.filter(pin => {
        if (tradableFilter === 'display') {
          return pin.availability === 'display';
        } else if (tradableFilter === 'trade') {
          return pin.availability === 'trade';
        } else if (tradableFilter === 'sale') {
          return pin.availability === 'sale';
        }
        return true;
      });
    }

    setFilteredPins(filtered);
  }, [searchTerm, tradableFilter, savedPins]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showTradableDropdown) {
        const target = event.target as Element;
        if (!target.closest('[data-dropdown="tradable-filter"]')) {
          setShowTradableDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showTradableDropdown]);

  const loadSavedPins = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      const pins = await pinsService.getSavedPins(user.id);
      setSavedPins(pins);
      setFilteredPins(pins);
    } catch (error) {
      console.error('Error loading saved pins:', error);
      showToast('Failed to load saved pins', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePinClick = (pinId: string) => {
    const pin = filteredPins.find(p => p.id === pinId);
    if (pin) {
      setSelectedPin(pin);
    }
  };

  const handlePinLike = async (pinId: string) => {
    try {
      const pin = filteredPins.find(p => p.id === pinId);
      if (pin) {
        const currentLikes = pin.likes || 0;
        const currentIsLiked = pin.isLiked || false;
        const newLikes = currentIsLiked ? currentLikes - 1 : currentLikes + 1;
        const newIsLiked = !currentIsLiked;
        
        await pinsService.updateLikes(pinId, newLikes, newIsLiked);
        
        // Update local state
        const updatePins = (pins: Pin[]) => pins.map(p => 
          p.id === pinId ? { ...p, likes: newLikes, isLiked: newIsLiked } : p
        );
        
        setSavedPins(updatePins);
        setFilteredPins(updatePins);
        
        showToast(newIsLiked ? 'Pin liked!' : 'Pin unliked!', 'success');
      }
    } catch (error) {
      console.error('Error updating pin like:', error);
      showToast('Failed to update like', 'error');
    }
  };

  const handlePinSave = async (pinId: string) => {
    try {
      const pin = filteredPins.find(p => p.id === pinId);
      if (pin) {
        const newIsSaved = !pin.isSaved;
        
        await pinsService.toggleSaved(pinId, newIsSaved);
        
        if (!newIsSaved) {
          // Remove from saved pins list
          const updatedPins = savedPins.filter(p => p.id !== pinId);
          setSavedPins(updatedPins);
          // The useEffect will handle filtering automatically
          showToast('Pin removed from saved', 'success');
        } else {
          showToast('Pin saved!', 'success');
        }
      }
    } catch (error) {
      console.error('Error updating pin save:', error);
      showToast('Failed to update save', 'error');
    }
  };

  const handleUserClick = (userId: string) => {
    navigate(`/profile/${userId}`);
  };

  const handlePinUpdate = (updatedPin: Pin) => {
    const updatePins = (pins: Pin[]) => pins.map(p => 
      p.id === updatedPin.id ? updatedPin : p
    );
    
    setSavedPins(updatePins);
    setFilteredPins(updatePins);
    setSelectedPin(updatedPin);
  };

  if (!user) {
    return (
      <CleanPageLayout
        title="Saved Pins"
      >
        <div className="text-center py-16">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gray-800 flex items-center justify-center">
            <BookmarkIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold text-white mb-3">
            Sign in to view saved pins
          </h2>
          <p className="text-gray-400 mb-6">
            You need to be signed in to see your saved pins.
          </p>
          <Button
            onClick={() => window.location.href = '/login'}
            variant="primary"
          >
            Sign In
          </Button>
        </div>
      </CleanPageLayout>
    );
  }

  return (
    <CleanPageLayout
      title="Saved Pins"
    >
      <PageToolbar
        left={
          <>
            {/* 1. Search - Always first, most common action */}
            <div className="w-full sm:w-64">
              <Input
                placeholder="Search saved pins..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<MagnifyingGlassIcon className="w-4 h-4" />}
                variant="default"
                className="w-full"
              />
            </div>

            {/* 2. View Toggle - Controls how content is displayed */}
            <ButtonGroup
              options={[
                {
                  value: 'masonry',
                  icon: (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z"/>
                    </svg>
                  ),
                  'aria-label': 'Masonry view'
                },
                {
                  value: 'grid',
                  icon: (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z"/>
                    </svg>
                  ),
                  'aria-label': 'Grid view'
                }
              ]}
              value={viewMode}
              onChange={(value) => {
                const newViewMode = value as 'grid' | 'masonry';
                setViewMode(newViewMode);
                localStorage.setItem('saved-pins-view-mode', newViewMode);
              }}
              size="md"
            />

            {/* 3. Filter - Refines search results */}
            {savedPins.length > 0 && (
              <div className="relative" data-dropdown="tradable-filter">
                <button
                  onClick={() => setShowTradableDropdown(!showTradableDropdown)}
                  className="flex items-center justify-between px-3 bg-gray-100 dark:bg-white/10 hover:bg-gray-200 dark:hover:bg-white/20 rounded-lg border border-gray-300 dark:border-white/20 text-sm text-gray-700 dark:text-white transition-all duration-200 backdrop-blur-sm"
                  style={{ height: '40px', minWidth: '120px' }}
                >
                  <div className="flex items-center space-x-2">
                    <span className={`w-3 h-3 rounded-full border flex-shrink-0 ${
                      tradableFilter === 'all' ? 'bg-gray-700 dark:bg-white border-gray-700 dark:border-white' :
                      tradableFilter === 'display' ? 'bg-gray-500 border-gray-500' :
                      tradableFilter === 'trade' ? 'bg-green-500 border-green-500' :
                      'bg-red-500 border-red-500'
                    }`}></span>
                    <span className="text-xs font-medium">
                      {tradableFilter === 'all' ? 'All' :
                       tradableFilter === 'display' ? 'Display' :
                       tradableFilter === 'trade' ? 'Trade' : 'Sale'}
                    </span>
                  </div>
                  <ChevronDownIcon className={`w-4 h-4 transition-transform duration-200 ${showTradableDropdown ? 'rotate-180' : ''}`} />
                </button>
                
                {/* Dropdown Menu */}
                {showTradableDropdown && (
                  <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-[60]">
                    <button
                      onClick={() => {
                        setTradableFilter('all');
                        setShowTradableDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-md last:rounded-b-md ${
                        tradableFilter === 'all' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      All
                    </button>
                    <button
                      onClick={() => {
                        setTradableFilter('display');
                        setShowTradableDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-md last:rounded-b-md ${
                        tradableFilter === 'display' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      Display Only
                    </button>
                    <button
                      onClick={() => {
                        setTradableFilter('trade');
                        setShowTradableDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-md last:rounded-b-md ${
                        tradableFilter === 'trade' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      Trade Only
                    </button>
                    <button
                      onClick={() => {
                        setTradableFilter('sale');
                        setShowTradableDropdown(false);
                      }}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-md last:rounded-b-md ${
                        tradableFilter === 'sale' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      Sale Only
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* 4. Sort - Last, most "meta" control */}
            {/* Note: Sort functionality can be added here in the future */}
          </>
        }
      />

      {/* Results Info */}
      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
        <span>
          {isLoading ? (
            'Loading...'
          ) : searchTerm.trim() ? (
            `${filteredPins.length} of ${savedPins.length} pins`
          ) : (
            `${savedPins.length} pins`
          )}
        </span>
        {searchTerm.trim() && (
          <>
            <span>•</span>
            <span>Searching for "{searchTerm}"</span>
          </>
        )}
        {tradableFilter !== 'all' && (
          <>
            <span>•</span>
            <span>{tradableFilter === 'display' ? 'Display only' :
                     tradableFilter === 'trade' ? 'Trade only' : 'Sale only'}</span>
          </>
        )}
      </div>

      {/* Main Content */}
        {isLoading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner size="lg" />
          </div>
        ) : savedPins.length === 0 ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center max-w-md mx-auto">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gray-200 dark:bg-gray-800 flex items-center justify-center">
                <BookmarkIcon className="w-8 h-8 text-gray-500 dark:text-gray-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                No saved pins yet
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Start saving pins you like to see them here
              </p>
              <Button
                onClick={() => navigate('/explore')}
                variant="primary"
              >
                Explore Pins
              </Button>
            </div>
          </div>
        ) : filteredPins.length === 0 ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center max-w-md mx-auto">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gray-200 dark:bg-gray-800 flex items-center justify-center">
                <MagnifyingGlassIcon className="w-8 h-8 text-gray-500 dark:text-gray-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                No pins found
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search terms
              </p>
              <Button
                onClick={() => setSearchTerm('')}
                variant="secondary"
              >
                Clear Search
              </Button>
            </div>
          </div>
        ) : (
          <div className={`grid gap-4 ${
            viewMode === 'masonry' 
              ? 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-5' 
              : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
          }`}>
            {filteredPins.map((pin) => (
              <PinCard
                key={pin.id}
                pin={pin}
                variant={viewMode}
                onLike={handlePinLike}
                onSave={handlePinSave}
                onComment={handlePinClick}
                onClick={handlePinClick}
                onUserClick={handleUserClick}
                showOverlayActions={viewMode === 'masonry'}
                showContent={viewMode === 'grid'}
              />
            ))}
          </div>
        )}

        {/* Pin Detail Modal */}
        {selectedPin && (
          <PinDetailModal
            pin={selectedPin}
            isOpen={!!selectedPin}
            onClose={() => setSelectedPin(null)}
            onLike={handlePinLike}
            onSave={handlePinSave}
            onShare={(pinId) => console.log('Share pin:', pinId)}
            onUserClick={handleUserClick}
            onUpdate={handlePinUpdate}
          />
        )}
      </CleanPageLayout>
    );
};

export default SavedPinsPage; 