import React, { useState, useEffect, Fragment } from 'react';
import { Switch, Listbox } from '@headlessui/react';
import { 
  BellIcon, 
  ShieldCheckIcon, 
  PaintBrushIcon,
  UserIcon,
  LockClosedIcon,
  EyeIcon,
  ChevronLeftIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  ChevronUpDownIcon,
  CheckIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  ArrowLeftOnRectangleIcon,
  TrashIcon,
  MapPinIcon,
  ChatBubbleLeftRightIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  ArrowLeftIcon,
  PlusIcon,
  PencilIcon,
  EnvelopeIcon,
  HomeIcon
} from '@heroicons/react/24/outline';
import { classNames } from '@/utils/classNames';
import { useTheme } from '@/theme/ThemeProvider';
import { useAuthStore } from '@/store/authStore';
import { useNavigate } from 'react-router-dom';
import { PageTemplate } from '@/components/layout/PageTemplate';
import { useScrollLock } from '@/hooks/useScrollLock';
import { Button } from '@/components/ui/design-system/Button';
import { colorTokens } from '@/components/ui/design-system/foundations';

import { ProfileSettings, type ProfileUser } from '@/components/ui/ProfileSettings';
import { usersService, type AdminUser, invalidateUserCache } from '@/services/usersService';
import { type User } from '@/types/user';
import { useToast } from '@/hooks/useToast';
import { googleAccountService } from '@/services/googleAccountService';
import { auth } from '@/config/firebase';
import { useShippingAddresses } from '@/hooks/useShippingAddresses';
import { useContactEmails, type ContactEmail } from '@/hooks/useContactEmails';
import { AddShippingAddressModal } from '@/modules/marketplace/components/AddShippingAddressModal';
import { EditEmailModal } from '@/components/ui/modals/EditEmailModal';
import type { ShippingAddress } from '@/types/shopping';
import { useQueryClient } from '@tanstack/react-query';

type SettingsSection = 'account' | 'password' | 'notifications' | 'privacy' | 'security' | 'appearance' | 'addresses';

// Componente de Modal de Confirmação
interface ConfirmationModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title: string;
  message: string;
  confirmText?: string;
  confirmVariant?: 'danger' | 'primary';
  requiresTyping?: boolean;
  requiredText?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onConfirm,
  onCancel,
  title,
  message,
  confirmText = "Confirm",
  confirmVariant = "primary",
  requiresTyping = false,
  requiredText = ""
}) => {
  const [typedText, setTypedText] = useState('');
  useScrollLock(isOpen);

  if (!isOpen) return null;

  const handleConfirm = () => {
    if (requiresTyping && typedText !== requiredText) {
      return;
    }
    onConfirm();
    setTypedText('');
  };

  const handleCancel = () => {
    onCancel();
    setTypedText('');
  };

  const isConfirmDisabled = requiresTyping && typedText !== requiredText;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-white/80 dark:bg-black/50 dark:bg-black/50/50 transition-opacity cursor-pointer"
          onClick={handleCancel}
        />
        
        {/* Modal */}
        <div className="relative bg-white dark:bg-custom-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 cursor-default">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {title}
              </h3>
        </div>
            <button
              onClick={handleCancel}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-600 dark:text-gray-300 transition-colors cursor-pointer"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
      </div>
          
          {/* Content */}
          <div className="mb-6">
            <p className="text-sm text-gray-600 dark:text-gray-300 whitespace-pre-line">
              {message}
            </p>
            
            {requiresTyping && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Type "{requiredText}" to confirm:
                </label>
                <input
                  type="text"
                  value={typedText}
                  onChange={(e) => setTypedText(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white bg-white dark:bg-gray-700 focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder={requiredText}
                />
      </div>
            )}
    </div>
          
          {/* Actions */}
          <div className="flex space-x-3 justify-end">
            <Button
              variant="secondary"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              variant={confirmVariant === 'danger' ? 'danger' : 'primary'}
              onClick={handleConfirm}
              disabled={isConfirmDisabled}
            >
              {confirmText}
            </Button>
          </div>
    </div>
      </div>
    </div>
  );
};

const SettingsPage: React.FC = () => {
  const { user, adminUser, setUser, setAdminUser: setAuthAdminUser } = useAuthStore();
  const { theme, setTheme } = useTheme();
  const { success, error: showError, info } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState<SettingsSection>('account');
  const [searchQuery, setSearchQuery] = useState('');
  const [showMobileSidebar, setShowMobileSidebar] = useState(true);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Modal states
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  // Google Account states
  const [googleAccountInfo, setGoogleAccountInfo] = useState(() => 
    googleAccountService.getGoogleAccountInfo(auth.currentUser)
  );
  const [isGoogleActionLoading, setIsGoogleActionLoading] = useState(false);

  // Addresses and emails states
  const [showAddAddressModal, setShowAddAddressModal] = useState(false);
  const [showEditAddressModal, setShowEditAddressModal] = useState(false);
  const [editingAddress, setEditingAddress] = useState<ShippingAddress | null>(null);
  const [showAddEmailInput, setShowAddEmailInput] = useState(false);
  const [showEditEmailModal, setShowEditEmailModal] = useState(false);
  const [editingEmail, setEditingEmail] = useState<ContactEmail | null>(null);
  const [newEmail, setNewEmail] = useState('');
  
  // Hooks for addresses and emails
  const { 
    addresses, 
    isLoading: isLoadingAddresses, 
    addAddress,
    updateAddress,
    deleteAddress 
  } = useShippingAddresses(user?.id || '');
  
  const { 
    emails: contactEmails, 
    isLoading: isLoadingEmails, 
    addEmail,
    updateEmail,
    deleteEmail: removeEmail 
  } = useContactEmails(user?.id || '');

  const isMobile = windowWidth < 768;

  // Update window width state when window is resized
  React.useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Reset to sidebar view when switching to mobile
  React.useEffect(() => {
    if (isMobile) {
      setShowMobileSidebar(true);
    }
  }, [isMobile]);

  // States for settings
  const [email, setEmail] = useState(user?.email || '');
  const [pendingEmail, setPendingEmail] = useState<string | null>(null);
  const [emailSaveStatus, setEmailSaveStatus] = useState<'idle' | 'sending' | 'pending' | 'error'>('idle');

  const [notificationSettings, setNotificationSettings] = useState(() => {
    // Initialize from adminUser data if available (has more detailed notification settings)
    if (adminUser?.notificationSettings) {
      return {
        globalNotifications: adminUser.notificationSettings.globalNotifications ?? adminUser.preferences.notificationsEnabled,
        newMessages: adminUser.notificationSettings.newMessages ?? adminUser.preferences.allowMessages,
        tradeRequests: adminUser.notificationSettings.tradeRequests ?? true,
        commentsPush: adminUser.notificationSettings.commentsPush ?? true,
        reactionsPush: adminUser.notificationSettings.reactionsPush ?? false,
        mentionsPush: adminUser.notificationSettings.mentionsPush ?? true,
        newPinsFromTraders: adminUser.notificationSettings.newPinsFromTraders ?? true,
      };
    }
    
    // Fallback to defaults based on user preferences
    return {
      globalNotifications: user?.preferences.notificationsEnabled ?? true,
      newMessages: user?.preferences.allowMessages ?? true,
      tradeRequests: true,
      commentsPush: true,
      reactionsPush: false,
      mentionsPush: true,
      newPinsFromTraders: true,
    };
  });
  const [privacySettings, setPrivacySettings] = useState(() => ({
    publicProfile: user?.preferences.publicProfile ?? true,
    allowMessages: user?.preferences.allowMessages ?? true,
    showLocation: user?.preferences.showLocation ?? true,
    allowComments: user?.preferences.allowComments ?? true,
  }));

  // Auto-save functions
  const saveEmail = async (newEmail: string) => {
    if (newEmail === user?.email) return;
    
    setEmailSaveStatus('sending');
    try {
      // TODO: Implement actual email confirmation API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      console.log('Email confirmation sent to:', newEmail);
      setPendingEmail(newEmail);
      setEmailSaveStatus('pending');
      info('Confirmation email sent. Check your inbox to verify the new email.');
    } catch (error) {
      console.error('Failed to send email confirmation:', error);
      setEmailSaveStatus('error');
      setTimeout(() => setEmailSaveStatus('idle'), 3000);
    }
  };

  const cancelEmailChange = () => {
    setEmail(user?.email || '');
    setPendingEmail(null);
    setEmailSaveStatus('idle');
  };

  const resendEmailConfirmation = () => {
    if (pendingEmail) {
      saveEmail(pendingEmail);
    }
  };

  // This would be called when user clicks the confirmation link in email
  const confirmEmailChange = async () => {
    if (!pendingEmail) return;
    
    try {
      // TODO: Implement actual email confirmation API call
      console.log('Email confirmed and updated to:', pendingEmail);
      // Update user email in the system
      setPendingEmail(null);
      setEmailSaveStatus('idle');
      success('Email successfully updated!');
    } catch (error) {
      console.error('Failed to confirm email change:', error);
      showError('Failed to confirm email change');
    }
  };

  const showAppToast = (message: string, type: 'success' | 'info' | 'error' = 'success') => {
    if (type === 'success') success(message);
    if (type === 'error') showError(message);
    if (type === 'info') info(message);
  };

  const loadSettingsFromPostgreSQL = async () => {
    if (!user) return;
    
    try {
      console.log('🔄 Loading settings from PostgreSQL for user:', user.id);
      
      // Load notification settings
      const notificationResponse = await fetch(`/api/users/settings/notifications?userId=${user.id}`);
      if (notificationResponse.ok) {
        const notificationData = await notificationResponse.json();
        setNotificationSettings({
          globalNotifications: notificationData.globalNotifications,
          newMessages: notificationData.newMessages,
          tradeRequests: notificationData.tradeRequests,
          commentsPush: notificationData.commentsPush,
          reactionsPush: notificationData.reactionsPush,
          mentionsPush: notificationData.mentionsPush,
          newPinsFromTraders: notificationData.newPinsFromTraders,
        });
        console.log('✅ Notification settings loaded from PostgreSQL:', notificationData);
      }
      
      // Load privacy settings
      const privacyResponse = await fetch(`/api/users/settings/privacy?userId=${user.id}`);
      if (privacyResponse.ok) {
        const privacyData = await privacyResponse.json();
        setPrivacySettings({
          publicProfile: privacyData.publicProfile,
          showLocation: privacyData.showLocation,
          allowMessages: privacyData.allowMessages,
          allowComments: privacyData.allowComments,
        });
        console.log('✅ Privacy settings loaded from PostgreSQL:', privacyData);
      }
      
    } catch (error) {
      console.error('❌ Failed to load settings from PostgreSQL:', error);
      // Keep using default/Firestore values if PostgreSQL fails
    }
  };

  // Load settings from PostgreSQL when user changes
  React.useEffect(() => {
    if (user) {
      loadSettingsFromPostgreSQL();
    }
  }, [user?.id]);

  const ensureUserInPostgreSQL = async () => {
    if (!user) return false;
    
    try {
      const response = await fetch('/api/users/auth/ensure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: user.id,
          email: user.email,
          username: user.username,
          displayName: `${user.firstName} ${user.lastName}`.trim() || user.username,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to ensure user in PostgreSQL: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      console.error('❌ Failed to ensure user in PostgreSQL:', error);
      return false;
    }
  };

  const saveNotificationSetting = async (key: string, value: boolean) => {
    if (!user) return;
    
    try {
      console.log('🔄 Saving notification setting:', key, value);
      
      // Ensure user exists in PostgreSQL first
      const userEnsured = await ensureUserInPostgreSQL();
      if (!userEnsured) {
        throw new Error('Failed to ensure user exists in database');
      }
      
      // Prepare settings object for PostgreSQL API
      const currentSettings = { ...notificationSettings, [key]: value };
      const settingsPayload = {
        userId: user.id,
        globalNotifications: currentSettings.globalNotifications,
        newMessages: currentSettings.newMessages,
        tradeRequests: currentSettings.tradeRequests,
        commentsPush: currentSettings.commentsPush,
        reactionsPush: currentSettings.reactionsPush,
        mentionsPush: currentSettings.mentionsPush,
        newPinsFromTraders: currentSettings.newPinsFromTraders,
      };
      
      // Call PostgreSQL API endpoint
      const response = await fetch('/api/users/settings/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsPayload),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save notification settings: ${response.status}`);
      }
      
      const savedSettings = await response.json();
      console.log('✅ Notification settings saved to PostgreSQL:', savedSettings);
      
      // Update local store
      if (user) {
        if (key === 'globalNotifications') {
          setUser({ ...user, preferences: { ...user.preferences, notificationsEnabled: value } });
        } else if (key === 'newMessages') {
          setUser({ ...user, preferences: { ...user.preferences, allowMessages: value } });
        }
      }
      
      console.log('✅ Notification setting updated successfully');
      showAppToast('Notification setting updated');
    } catch (error) {
      console.error('❌ Failed to update notification setting:', error);
      showAppToast('Failed to update notification setting', 'error');
      
      // Revert the local state change
      setNotificationSettings(prev => ({ ...prev, [key]: !value }));
    }
  };

  const savePrivacySetting = async (key: string, value: boolean) => {
    if (!user) return;
    
    try {
      console.log('🔄 Saving privacy setting:', key, value);
      
      // Ensure user exists in PostgreSQL first
      const userEnsured = await ensureUserInPostgreSQL();
      if (!userEnsured) {
        throw new Error('Failed to ensure user exists in database');
      }
      
      // Prepare settings object for PostgreSQL API
      const currentSettings = { ...privacySettings, [key]: value };
      const settingsPayload = {
        userId: user.id,
        publicProfile: currentSettings.publicProfile,
        showLocation: currentSettings.showLocation,
        allowMessages: currentSettings.allowMessages,
        allowComments: currentSettings.allowComments,
      };
      
      // Call PostgreSQL API endpoint
      const response = await fetch('/api/users/settings/privacy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsPayload),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save privacy settings: ${response.status}`);
      }
      
      const savedSettings = await response.json();
      console.log('✅ Privacy settings saved to PostgreSQL:', savedSettings);
      
      // Update local store with correct preference mapping
      const preferenceUpdates: Partial<User['preferences']> = {};
      
      if (key === 'publicProfile') {
        preferenceUpdates.publicProfile = value;
      } else if (key === 'allowMessages') {
        preferenceUpdates.allowMessages = value;
      } else if (key === 'showLocation') {
        preferenceUpdates.showLocation = value;
      } else if (key === 'allowComments') {
        preferenceUpdates.allowComments = value;
      }
      
      if (Object.keys(preferenceUpdates).length > 0 && user) {
        setUser({ ...user, preferences: { ...user.preferences, ...preferenceUpdates } });
        try {
          // Keep Firestore in sync so that auth store and other Firebase-backed features stay consistent
          await usersService.updatePreferences(user.id, preferenceUpdates);
          console.log('✅ Firestore preferences updated for user', user.id);
        } catch (firestoreErr) {
          console.warn('⚠️ Failed to update preferences in Firestore (will still be correct in PostgreSQL):', firestoreErr);
        }
      }
      
      // Force refresh user data from PostgreSQL to ensure consistency
      await refreshUserDataFromPostgreSQL();
      
      console.log('✅ Privacy setting updated successfully');
      showAppToast('Privacy setting updated');
    } catch (error) {
      console.error('❌ Failed to update privacy setting:', error);
      showAppToast('Failed to update privacy setting', 'error');
      
      // Revert the local state change
      setPrivacySettings(prev => ({ ...prev, [key]: !value }));
    }
  };

  // Function to refresh user data from PostgreSQL
  const refreshUserDataFromPostgreSQL = async () => {
    if (!user) return;
    
    try {
      console.log('🔄 Refreshing user data from PostgreSQL...');
      
      // Invalidate cache first to force fresh data
      invalidateUserCache(user.id);
      
      const response = await fetch(`/api/users/profile/${user.id}`);
      if (response.ok) {
        const updatedUserData = await response.json();
        
        // Update the user store with fresh data from PostgreSQL
        const updatedUser: User = {
          ...user,
          preferences: {
            ...user.preferences,
            ...updatedUserData.preferences
          }
        };
        
        setUser(updatedUser);
        // Invalidate react-query caches
        queryClient.invalidateQueries({ predicate: (query)=> Array.isArray(query.queryKey) && query.queryKey[0]==='user' });
        console.log('✅ User data refreshed from PostgreSQL:', updatedUserData.preferences);
      }
    } catch (error) {
      console.error('❌ Failed to refresh user data from PostgreSQL:', error);
    }
  };

  const saveTheme = async (newTheme: string) => {
    if (!user) return;
    
    try {
      console.log('🔄 Saving theme preference:', newTheme);
      
      // Ensure user exists in PostgreSQL first
      const userEnsured = await ensureUserInPostgreSQL();
      if (!userEnsured) {
        throw new Error('Failed to ensure user exists in database');
      }
      
      // Call PostgreSQL API endpoint for appearance settings
      const response = await fetch('/api/users/settings/appearance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          theme: newTheme,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save theme: ${response.status}`);
      }
      
      const savedSettings = await response.json();
      console.log('✅ Theme saved to PostgreSQL:', savedSettings);
      
      console.log('✅ Theme updated successfully to:', newTheme);
      showAppToast('Theme updated');
    } catch (error) {
      console.error('❌ Failed to update theme:', error);
      showAppToast('Failed to update theme', 'error');
    }
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotificationSettings(prev => ({ ...prev, [key]: value }));
    saveNotificationSetting(key, value);
  };

  const handlePrivacyChange = (key: string, value: boolean) => {
    setPrivacySettings(prev => ({ ...prev, [key]: value }));
    savePrivacySetting(key, value);
  };

  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
  };

  const handleEmailBlur = () => {
    if (email !== user?.email && email.trim() !== '') {
      saveEmail(email);
    }
  };

  const handleThemeChange = (newTheme: string) => {
    setTheme(newTheme as 'light' | 'dark' | 'system');
    saveTheme(newTheme);
  };

  // Debounce email changes
  React.useEffect(() => {
    if (email === user?.email || email.trim() === '') return;
    
    const timeoutId = setTimeout(() => {
      saveEmail(email);
    }, 2000); // Save after 2 seconds of no typing

    return () => clearTimeout(timeoutId);
  }, [email, user?.email]);

  const handleDeactivateAccount = () => {
    setShowDeactivateModal(true);
  };

  const handleDeleteAccount = () => {
    setShowDeleteModal(true);
  };

  const confirmDeactivateAccount = () => {
    // TODO: Implement account deactivation
    info('Account deactivation functionality will be implemented');
    setShowDeactivateModal(false);
  };

  const confirmDeleteAccount = () => {
    // TODO: Implement account deletion
    info('Account deletion functionality will be implemented');
    setShowDeleteModal(false);
  };

  // Google Account functions
  const connectGoogleAccount = async () => {
    setIsGoogleActionLoading(true);
    try {
      const result = await googleAccountService.connectGoogleAccount();
      
      if (result.success) {
        // Update Google account info state
        setGoogleAccountInfo(googleAccountService.getGoogleAccountInfo(auth.currentUser));
        showAppToast('Google account connected successfully!');
      } else {
        showAppToast(result.message, 'error');
      }
    } catch (error: any) {
      console.error('Error connecting Google account:', error);
      showAppToast('Failed to connect Google account', 'error');
    } finally {
      setIsGoogleActionLoading(false);
    }
  };

  const disconnectGoogleAccount = async () => {
    try {
      setIsGoogleActionLoading(true);
      const result = await googleAccountService.disconnectGoogleAccount();
      
      if (result.success) {
        setGoogleAccountInfo(null);
        showAppToast('Google account disconnected successfully', 'success');
      } else {
        showAppToast(result.message || 'Failed to disconnect Google account', 'error');
      }
    } catch (error: any) {
      console.error('Error disconnecting Google account:', error);
      showAppToast('Failed to disconnect Google account', 'error');
    } finally {
      setIsGoogleActionLoading(false);
    }
  };

  // Update Google account info when user changes
  React.useEffect(() => {
    setGoogleAccountInfo(googleAccountService.getGoogleAccountInfo(auth.currentUser));
  }, [user]);

  // Address and Email Management Functions
  const handleAddAddress = async (newAddress: any) => {
    try {
      await addAddress(newAddress);
      setShowAddAddressModal(false);
      showAppToast('Address added successfully', 'success');
    } catch (error: any) {
      showAppToast(error.message || 'Failed to add address', 'error');
    }
  };

  const handleEditAddress = (address: ShippingAddress) => {
    setEditingAddress(address);
    setShowEditAddressModal(true);
  };

  const handleUpdateAddress = async (updatedAddress: ShippingAddress) => {
    try {
      await updateAddress(updatedAddress.id, updatedAddress);
      setShowEditAddressModal(false);
      setEditingAddress(null);
      showAppToast('Address updated successfully', 'success');
    } catch (error: any) {
      showAppToast(error.message || 'Failed to update address', 'error');
    }
  };

  const handleDeleteAddress = async (addressId: string) => {
    try {
      await deleteAddress(addressId);
      showAppToast('Address deleted successfully', 'success');
    } catch (error: any) {
      showAppToast(error.message || 'Failed to delete address', 'error');
    }
  };

  const handleAddEmail = async () => {
    if (newEmail && newEmail.includes('@')) {
      try {
        await addEmail(newEmail);
        setNewEmail('');
        setShowAddEmailInput(false);
        showAppToast('Email added successfully', 'success');
      } catch (error: any) {
        showAppToast(error.message || 'Failed to add email', 'error');
      }
    } else {
      showAppToast('Please enter a valid email address', 'error');
    }
  };

  const handleEditEmail = (email: ContactEmail) => {
    setEditingEmail(email);
    setShowEditEmailModal(true);
  };

  const handleUpdateEmail = async (emailId: string, updates: Partial<ContactEmail>) => {
    try {
      await updateEmail(emailId, updates);
      setShowEditEmailModal(false);
      setEditingEmail(null);
      showAppToast('Email updated successfully', 'success');
    } catch (error: any) {
      showAppToast(error.message || 'Failed to update email', 'error');
    }
  };

  const handleRemoveEmail = async (emailId: string) => {
    try {
      await removeEmail(emailId);
      showAppToast('Email removed successfully', 'success');
    } catch (error: any) {
      showAppToast(error.message || 'Failed to remove email', 'error');
    }
  };

  const handleSaveProfile = async (updatedUserFromForm: Partial<ProfileUser>): Promise<AdminUser | void> => {
    if (!user) {
      showAppToast('User not found. Cannot save profile.', 'error');
      return;
    }
    
    try {
      console.log('🔍 SettingsPage.handleSaveProfile - Dados recebidos do ProfileSettings:', JSON.stringify(updatedUserFromForm, null, 2));
      console.log('📞 SettingsPage.handleSaveProfile - phoneNumber recebido:', updatedUserFromForm.phoneNumber);
      console.log('👤 User ID para update:', user.id);
      
      const savedUser = await usersService.update(user.id, updatedUserFromForm);
      
      console.log('✅ SettingsPage.handleSaveProfile - usersService.update executado com sucesso');
      console.log('↪️ SavedUser (retornado pelo service):', JSON.stringify(savedUser, null, 2));

      if (savedUser) {
        // Atualizar o estado global no authStore
        if (user) {
          setUser({
            ...user,
            firstName: savedUser.firstName,
            lastName: savedUser.lastName,
            username: savedUser.username,
            bio: savedUser.bio,
            location: savedUser.location,
            phoneNumber: savedUser.phoneNumber,
            avatarUrl: savedUser.avatarUrl
          });
        }
        setAuthAdminUser(savedUser); // Atualiza o adminUser completo no store
        
        // Se a role foi alterada no formulário e salva, o adminUser já reflete isso.
        // As permissões são recalculadas no authStore quando a role muda.
        
        console.log('✅ SettingsPage.handleSaveProfile - authStore atualizado com savedUser');
        showAppToast('Profile updated successfully');
        return savedUser; // <-- RETORNAR O USUÁRIO SALVO
      } else {
        showAppToast('Profile update failed. No data returned from service.', 'error');
      }
    } catch (error) {
      console.error('❌ SettingsPage.handleSaveProfile - Erro ao atualizar perfil:', error);
      showAppToast('Failed to update profile. See console for details.', 'error');
    }
  };

  const sidebarItems = [
    { 
      id: 'account', 
      label: 'Profile Settings', 
      icon: UserIcon,
      searchTerms: ['profile', 'account', 'email', 'name', 'bio', 'avatar', 'location', 'phone']
    },
    { 
      id: 'password', 
      label: 'Password & Security', 
      icon: LockClosedIcon,
      searchTerms: ['password', 'security', 'change password', 'authentication']
    },
    { 
      id: 'notifications', 
      label: 'Notifications', 
      icon: BellIcon,
      searchTerms: ['notifications', 'messages', 'trade requests', 'comments', 'reactions', 'mentions', 'pins', 'global']
    },
    { 
      id: 'privacy', 
      label: 'Privacy & Data', 
      icon: EyeIcon,
      searchTerms: ['privacy', 'data', 'public profile', 'allow messages', 'allow comments', 'visible', 'content']
    },
    { 
      id: 'security', 
      label: 'Security', 
      icon: LockClosedIcon,
      searchTerms: ['security', 'two-factor', '2fa', 'authentication', 'google account', 'sessions', 'connected']
    },
    { 
      id: 'appearance', 
      label: 'Appearance', 
      icon: PaintBrushIcon,
      searchTerms: ['appearance', 'theme', 'light', 'dark', 'system', 'mode']
    },
    { 
      id: 'addresses', 
      label: 'Contact Info', 
      icon: HomeIcon,
      searchTerms: ['contact', 'addresses', 'shipping', 'email', 'info']
    },
  ];

  const filteredItems = sidebarItems.filter(item => {
    const query = searchQuery.toLowerCase();
    return item.label.toLowerCase().includes(query) || 
           item.searchTerms.some(term => term.toLowerCase().includes(query));
  });

  const renderContent = () => {
    const section = sidebarItems.find(item => item.id === activeSection);
    if (!section) return null;

    return (
      <div className="flex flex-col h-full">
        {/* Content Header */}
        <div className={classNames(
          "pb-6 mb-8",
          isMobile ? "px-6 pt-6" : "px-8 pt-8"
        )}>
          {isMobile && (
            <button
              onClick={handleBackToSidebar}
              className="mb-4 p-2 hover:bg-custom-gray-800 rounded-lg transition-colors"
            >
              <ChevronLeftIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
          )}
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {section.label}
          </h2>
          <p className="text-gray-500 dark:text-gray-400">
            {section.id === 'account' && 'Manage your public profile information'}
            {section.id === 'password' && 'Change your password and manage account security'}
            {section.id === 'notifications' && 'Control how and when you receive notifications'}
            {section.id === 'privacy' && 'Manage your privacy and data sharing preferences'}
            {section.id === 'security' && 'Secure your account with advanced security features'}
            {section.id === 'appearance' && 'Customize the look and feel of your interface'}
            {section.id === 'addresses' && 'Manage your contact information, shipping addresses and emails'}
          </p>
        </div>

        {/* Content Body */}
        <div className={classNames(
          "flex-1 overflow-y-auto scrollbar-theme"
        )}>
          {activeSection === 'account' && user && (
            <div>
              {/* Profile Settings Component */}
              <div className={classNames(
                "max-w-4xl",
                isMobile ? "px-6 pb-6" : "px-8 pb-8"
              )}>
                <ProfileSettings
                  user={user}
                  onSave={handleSaveProfile}
                  className="bg-transparent border-0 shadow-none p-0"
                />
              </div>
            </div>
          )}

          {activeSection === 'password' && (
            <div className={classNames(
              "max-w-4xl",
              isMobile ? "px-6 pb-6" : "px-8 pb-8"
            )}>
              {/* Password Section */}
              <div className="mb-12">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Change Password</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">
                      Current Password
                    </label>
                    <button className="text-blue-400 hover:text-blue-300 text-sm font-medium">
                      Change Password
                    </button>
                  </div>
                </div>
              </div>

              {/* Danger Zone */}
              <div className="pt-8">
                <h3 className="text-lg font-semibold text-red-600 dark:text-red-400 mb-6">Danger Zone</h3>
                <div className="space-y-4 max-w-2xl">
                  <div className="p-6 border border-red-800 rounded-lg bg-red-900/10">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 mr-4">
                        <h4 className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">Deactivate Account</h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Temporarily disable your account. You can reactivate it anytime.
                        </p>
                      </div>
                      <button
                        onClick={() => setShowDeactivateModal(true)}
                        className="flex-shrink-0 px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 border border-red-800 rounded-lg hover:bg-red-900/20 transition-colors"
                      >
                        Deactivate
                      </button>
                    </div>
                  </div>

                  <div className="p-6 border border-red-800 rounded-lg bg-red-900/10">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 mr-4">
                        <h4 className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">Delete Account</h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Permanently delete your account and all associated data.
                        </p>
                      </div>
                      <button
                        onClick={() => setShowDeleteModal(true)}
                        className="flex-shrink-0 px-4 py-2 text-sm font-medium text-gray-900 dark:text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'notifications' && (
            <div className={classNames(
              "space-y-1 max-w-4xl",
              isMobile ? "px-6 pb-6" : "px-8 pb-8"
            )}>
              {[
                {
                  key: 'globalNotifications',
                  title: 'Global Notifications',
                  description: 'Receive all types of notifications',
                  icon: BellIcon
                },
                {
                  key: 'newMessages',
                  title: 'New Messages',
                  description: 'Get notified when you receive new messages',
                  icon: ChatBubbleLeftRightIcon
                },
                {
                  key: 'tradeRequests',
                  title: 'Trade Requests',
                  description: 'Notifications for incoming trade requests',
                  icon: UserIcon
                },
                {
                  key: 'commentsPush',
                  title: 'Comments',
                  description: 'When someone comments on your pins',
                  icon: ChatBubbleLeftRightIcon
                },
                {
                  key: 'reactionsPush',
                  title: 'Reactions',
                  description: 'When someone reacts to your content',
                  icon: UserIcon
                },
                {
                  key: 'mentionsPush',
                  title: 'Mentions',
                  description: 'When someone mentions you',
                  icon: UserIcon
                },
                {
                  key: 'newPinsFromTraders',
                  title: 'New Pins from Traders',
                  description: 'When traders you follow add new pins',
                  icon: MapPinIcon
                }
              ].map((item, index, array) => (
                <div key={item.key} className="flex items-center justify-between py-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <item.icon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">{item.title}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{item.description}</p>
                    </div>
                  </div>
                  <Switch
                    checked={notificationSettings[item.key as keyof typeof notificationSettings]}
                    onChange={(checked) => handleNotificationChange(item.key, checked)}
                    className={classNames(
                      'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900',
                      notificationSettings[item.key as keyof typeof notificationSettings] ? 'bg-blue-600' : 'bg-gray-600'
                    )}
                  >
                    <span
                      className={classNames(
                        'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                        notificationSettings[item.key as keyof typeof notificationSettings] ? 'translate-x-6' : 'translate-x-1'
                      )}
                    />
                  </Switch>
                </div>
              ))}
            </div>
          )}

          {activeSection === 'privacy' && (
            <div className={classNames(
              "space-y-1 max-w-4xl",
              isMobile ? "px-6 pb-6" : "px-8 pb-8"
            )}>
              {[
                {
                  key: 'publicProfile',
                  title: 'Public Profile',
                  description: 'Make your profile visible to other users',
                  icon: EyeIcon
                },
                {
                  key: 'showLocation',
                  title: 'Show Location',
                  description: 'Display your location on your profile',
                  icon: MapPinIcon
                },
                {
                  key: 'allowMessages',
                  title: 'Allow Messages',
                  description: 'Allow other users to send you direct messages',
                  icon: ChatBubbleLeftRightIcon
                },
                {
                  key: 'allowComments',
                  title: 'Allow Comments',
                  description: 'Allow users to comment on your pins and boards',
                  icon: ChatBubbleLeftRightIcon
                }
              ].map((item, index, array) => (
                <div key={item.key} className="flex items-center justify-between py-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <item.icon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">{item.title}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{item.description}</p>
                    </div>
                  </div>
                  <Switch
                    checked={privacySettings[item.key as keyof typeof privacySettings]}
                    onChange={(checked) => handlePrivacyChange(item.key, checked)}
                    className={classNames(
                      'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900',
                      privacySettings[item.key as keyof typeof privacySettings] ? 'bg-blue-600' : 'bg-gray-600'
                    )}
                  >
                    <span
                      className={classNames(
                        'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                        privacySettings[item.key as keyof typeof privacySettings] ? 'translate-x-6' : 'translate-x-1'
                      )}
                    />
                  </Switch>
                </div>
              ))}
            </div>
          )}

          {activeSection === 'security' && (
            <div className={classNames(
              "space-y-1 max-w-4xl",
              isMobile ? "px-6 pb-6" : "px-8 pb-8"
            )}>
              <div className="py-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <ShieldCheckIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Two-Factor Authentication</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Add an extra layer of security to your account</p>
                    </div>
                  </div>
                  <button className="text-blue-400 hover:text-blue-300 text-sm font-medium">
                    Enable
                  </button>
                </div>
              </div>

              <div className="py-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <LockClosedIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Connected Google Account</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your Google account connection</p>
                    </div>
                  </div>
                  <button className="text-blue-400 hover:text-blue-300 text-sm font-medium">
                    Manage
                  </button>
                </div>
              </div>

              <div className="py-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <UserIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">Active Sessions</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage devices that are signed in to your account</p>
                    </div>
                  </div>
                  <button className="text-blue-400 hover:text-blue-300 text-sm font-medium">
                    View All
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'appearance' && (
            <div className={classNames(
              "max-w-4xl",
              isMobile ? "px-6 pb-6" : "px-8 pb-8"
            )}>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Theme</h3>
                <Listbox value={theme} onChange={(value) => setTheme(value as 'light' | 'dark' | 'system')}>
                  <div className="relative">
                    <Listbox.Button className="relative w-full cursor-pointer rounded-lg bg-custom-gray-800 py-3 pl-4 pr-10 text-left text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <span className="flex items-center">
                        {theme === 'light' && <SunIcon className="h-5 w-5 mr-3 text-yellow-500" />}
                        {theme === 'dark' && <MoonIcon className="h-5 w-5 mr-3 text-blue-400" />}
                        {theme === 'system' && <ComputerDesktopIcon className="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" />}
                        <span className="block truncate capitalize">{theme}</span>
                      </span>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <ChevronUpDownIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      </span>
                    </Listbox.Button>

                    <Listbox.Options className="absolute z-[9999] mt-1 max-h-60 w-full overflow-auto scrollbar-theme rounded-lg bg-custom-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      {[
                        { value: 'light', label: 'Light', icon: SunIcon },
                        { value: 'dark', label: 'Dark', icon: MoonIcon },
                        { value: 'system', label: 'System', icon: ComputerDesktopIcon }
                      ].map((option) => (
                        <Listbox.Option
                          key={option.value}
                          className={({ active }) =>
                            classNames(
                              'relative cursor-pointer select-none py-3 pl-4 pr-9',
                              active ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : 'text-gray-600 dark:text-gray-300'
                            )
                          }
                          value={option.value}
                        >
                          {({ selected, active }) => (
                            <>
                              <div className="flex items-center">
                                <option.icon className="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" />
                                <span className={classNames('block truncate', selected ? 'font-medium' : 'font-normal')}>
                                  {option.label}
                                </span>
                              </div>

                              {selected && (
                                <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-400">
                                  <CheckIcon className="h-5 w-5" />
                                </span>
                              )}
                            </>
                          )}
                        </Listbox.Option>
                      ))}
                    </Listbox.Options>
                  </div>
                </Listbox>
              </div>
            </div>
          )}

          {activeSection === 'addresses' && (
            <div className={classNames(
              "max-w-4xl",
              isMobile ? "px-6 pb-6" : "px-8 pb-8"
            )}>
              {/* Shipping Addresses Section */}
              <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Shipping Addresses</h3>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setShowAddAddressModal(true)}
                    className="inline-flex items-center space-x-2"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add Address</span>
                  </Button>
                </div>

                {isLoadingAddresses ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  </div>
                ) : addresses.length === 0 ? (
                  <div className="text-center py-8 bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <HomeIcon className="w-12 h-12 text-gray-500 dark:text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">No shipping addresses found</p>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => setShowAddAddressModal(true)}
                      className="inline-flex items-center space-x-2"
                    >
                      <PlusIcon className="w-4 h-4" />
                      <span>Add Your First Address</span>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {addresses.map((address) => (
                      <div key={address.id} className="p-4 bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="text-gray-900 dark:text-white font-medium">{address.addressName}</h4>
                              {address.isDefault && (
                                <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-0.5 rounded">
                                  Default
                                </span>
                              )}
                            </div>
                            <p className="text-gray-900 dark:text-white text-sm">{address.fullName}</p>
                            <p className="text-gray-500 dark:text-gray-400 text-sm">{address.addressLine1}</p>
                            {address.addressLine2 && (
                              <p className="text-gray-500 dark:text-gray-400 text-sm">{address.addressLine2}</p>
                            )}
                            <p className="text-gray-500 dark:text-gray-400 text-sm">
                              {address.city}, {address.state} {address.postalCode}
                            </p>
                            {address.phone && (
                              <p className="text-gray-500 dark:text-gray-400 text-sm">{address.phone}</p>
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <button 
                              type="button"
                              className="text-blue-400 hover:text-blue-300 p-2"
                              onClick={() => handleEditAddress(address)}
                            >
                              <PencilIcon className="w-4 h-4" />
                            </button>
                            <button 
                              type="button"
                              className="text-red-600 dark:text-red-400 hover:text-red-300 p-2"
                              onClick={() => handleDeleteAddress(address.id)}
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Contact Emails Section */}
              <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Contact Emails</h3>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setShowAddEmailInput(true)}
                    className="inline-flex items-center space-x-2"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add Email</span>
                  </Button>
                </div>

                {isLoadingEmails ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  </div>
                ) : contactEmails.length === 0 && !showAddEmailInput ? (
                  <div className="text-center py-8 bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <EnvelopeIcon className="w-12 h-12 text-gray-500 dark:text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">No contact emails found</p>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => setShowAddEmailInput(true)}
                      className="inline-flex items-center space-x-2"
                    >
                      <PlusIcon className="w-4 h-4" />
                      <span>Add Your First Email</span>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {contactEmails.map((email) => (
                      <div key={email.id} className="p-4 bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <EnvelopeIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                            <div>
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-900 dark:text-white text-sm font-medium">{email.email}</span>
                                {email.isVerified && (
                                  <CheckCircleIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                                )}
                              </div>
                              {email.emailName && (
                                <span className="text-xs text-gray-500 dark:text-gray-400">({email.emailName})</span>
                              )}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <button 
                              type="button"
                              className="text-blue-400 hover:text-blue-300 p-2"
                              onClick={() => handleEditEmail(email)}
                            >
                              <PencilIcon className="w-4 h-4" />
                            </button>
                            <button 
                              type="button"
                              className="text-red-600 dark:text-red-400 hover:text-red-300 p-2"
                              onClick={() => handleRemoveEmail(email.id)}
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}

                    {showAddEmailInput && (
                      <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                        <h4 className="text-gray-900 dark:text-white font-medium mb-3">Add New Email</h4>
                        <div className="space-y-3">
                          <input
                            type="email"
                            value={newEmail}
                            onChange={(e) => setNewEmail(e.target.value)}
                            placeholder="Enter email address"
                            className="w-full p-3 bg-white/5 border border-gray-200 dark:border-white/10 rounded text-gray-900 dark:text-white placeholder-gray-400 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            onKeyPress={(e) => e.key === 'Enter' && handleAddEmail()}
                          />
                          <div className="flex space-x-3">
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={handleAddEmail}
                              disabled={!newEmail.includes('@')}
                            >
                              Add Email
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setShowAddEmailInput(false);
                                setNewEmail('');
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}


                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleSectionSelect = (sectionId: SettingsSection) => {
    setActiveSection(sectionId);
    if (isMobile) {
      setShowMobileSidebar(false);
    }
  };

  const handleBackToSidebar = () => {
    setShowMobileSidebar(true);
  };

  return (
    <PageTemplate 
      title="Settings"
      hideBackButton={false}
      onBackClick={handleBack}
      noPadding={true}
    >
      <div className="flex h-[calc(100vh-8rem)] -mx-4">
        {/* Sidebar */}
        <div className={classNames(
          "bg-gray-50 dark:bg-gray-900",
          isMobile 
            ? (showMobileSidebar ? "w-full" : "hidden")
            : "w-80"
        )}>
          {/* Search */}
          <div className="p-4">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500 dark:text-gray-400" />
              <input
                type="text"
                placeholder="Search settings..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-3 pl-10 pr-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
            </div>
          </div>

          {/* Navigation */}
          <div className="p-4">
            <nav className="space-y-1">
              {filteredItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleSectionSelect(item.id as SettingsSection)}
                  className={classNames(
                    'w-full flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors',
                    activeSection === item.id
                      ? 'bg-blue-600 text-gray-900 dark:text-white'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:text-white hover:bg-custom-gray-800'
                  )}
                >
                  <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                  <span className="text-left">{item.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Vertical Divider */}
        {!isMobile && (
          <div className="flex flex-col">
            <div className="h-4"></div>
            <div className="flex-1 w-px" style={{ backgroundColor: colorTokens.border.subtle }}></div>
            <div className="h-4"></div>
          </div>
        )}

        {/* Content */}
        {(!isMobile || !showMobileSidebar) && (
          <div className={`${isMobile ? 'w-full' : 'flex-1'} bg-white dark:bg-black`}>
            {renderContent()}
          </div>
        )}
      </div>

      {/* Confirmation Modals */}
      <ConfirmationModal
        isOpen={showDeactivateModal}
        onConfirm={confirmDeactivateAccount}
        onCancel={() => setShowDeactivateModal(false)}
        title="Deactivate Account"
        message="Are you sure you want to deactivate your account?

This will temporarily hide your profile, pins, and boards from other users. You can reactivate your account at any time by logging back in."
        confirmText="Deactivate"
        confirmVariant="danger"
      />

      <ConfirmationModal
        isOpen={showDeleteModal}
        onConfirm={confirmDeleteAccount}
        onCancel={() => setShowDeleteModal(false)}
        title="Delete Account"
        message="Are you sure you want to permanently delete your account?

This action cannot be undone and will permanently delete:
• Your profile and all personal information
• All your pins and boards
• Your trading history
• All messages and conversations"
        confirmText="Delete Account"
        confirmVariant="danger"
        requiresTyping={true}
        requiredText="DELETE"
      />

      {/* Add Shipping Address Modal */}
      <AddShippingAddressModal
        isOpen={showAddAddressModal}
        onClose={() => setShowAddAddressModal(false)}
        onAddressAdded={handleAddAddress}
        userId={user?.id || ''}
        addAddressFunction={addAddress}
      />

      {/* Edit Shipping Address Modal */}
      {editingAddress && (
        <AddShippingAddressModal
          isOpen={showEditAddressModal}
          onClose={() => {
            setShowEditAddressModal(false);
            setEditingAddress(null);
          }}
          onAddressAdded={handleUpdateAddress}
          userId={user?.id || ''}
          addAddressFunction={addAddress}
        />
      )}

      {/* Edit Email Modal */}
      <EditEmailModal
        isOpen={showEditEmailModal}
        onClose={() => {
          setShowEditEmailModal(false);
          setEditingEmail(null);
        }}
        email={editingEmail}
        onEmailUpdated={handleUpdateEmail}
      />
    </PageTemplate>
  );
};

export { SettingsPage }; 