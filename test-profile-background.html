<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Background Fix</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Simular as variáveis CSS do projeto */
        :root {
            --color-white: #ffffff;
            --color-black: #000000;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --border-primary: #e2e8f0;
            color-scheme: light;
            color: #1e293b;
        }
        
        .dark {
            --bg-primary: #000000;
            --bg-secondary: #0a0a0a;
            --bg-tertiary: #171717;
            --text-primary: rgba(255, 255, 255, 0.95);
            --text-secondary: rgba(255, 255, 255, 0.7);
            --border-primary: #262626;
            color-scheme: dark;
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* Simular o body do projeto */
        body {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        /* Simular as correções aplicadas */
        .sidebar-container {
            background-color: white !important;
            border-color: var(--border-primary);
        }
        
        .dark .sidebar-container {
            background-color: black !important;
        }
    </style>
</head>
<body>
    <div class="min-h-screen">
        <h1 class="text-2xl font-bold p-6 text-gray-900 dark:text-white">Test Profile Background Fix</h1>
        
        <div class="space-y-6 p-6">
            <!-- Teste 1: PageTemplate simulado -->
            <div class="bg-white dark:bg-black min-h-[400px] rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Teste 1: PageTemplate</h2>
                <div class="bg-white dark:bg-black p-4 rounded">
                    <p class="text-gray-700 dark:text-gray-300">Header sticky com bg-white dark:bg-black</p>
                </div>
                <div class="mt-4 space-y-4">
                    <!-- Avatar placeholder -->
                    <div class="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span class="text-gray-500 dark:text-gray-400">Avatar</span>
                    </div>
                    <!-- Follow button -->
                    <button class="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 px-6 py-2 rounded-lg">
                        Following
                    </button>
                </div>
            </div>
            
            <!-- Teste 2: CleanPageLayout simulado -->
            <div class="bg-white dark:bg-black min-h-[400px] rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Teste 2: CleanPageLayout</h2>
                <div class="w-full p-4 sm:bg-white sm:dark:bg-gray-800 sm:rounded-2xl sm:shadow-lg sm:max-w-2xl sm:mx-auto sm:p-10 sm:dark:border sm:dark:border-gray-700">
                    <div class="flex flex-col items-center gap-2 mb-6">
                        <div class="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400">Profile</span>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">User Profile</h3>
                        <p class="text-gray-600 dark:text-gray-400">Profile content here</p>
                    </div>
                </div>
            </div>
            
            <!-- Teste 3: ProfilePage loading simulado -->
            <div class="bg-white dark:bg-black min-h-[200px] rounded-lg border border-gray-200 dark:border-gray-700 flex items-center justify-center">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <p class="text-gray-600 dark:text-gray-400">Loading Profile...</p>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <button onclick="toggleDarkMode()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-2">
                Toggle Dark Mode
            </button>
            
            <button onclick="debugStyles()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Debug Styles
            </button>
            
            <div class="mt-4 p-4 bg-yellow-100 dark:bg-yellow-900 rounded">
                <h3 class="font-semibold">Status Esperado:</h3>
                <ul class="list-disc list-inside text-sm mt-2">
                    <li>✅ Light Mode: Todos os testes devem ter fundo BRANCO</li>
                    <li>✅ Dark Mode: Todos os testes devem ter fundo PRETO</li>
                    <li>✅ Avatar: Cinza claro no light mode, cinza escuro no dark mode</li>
                    <li>✅ Botões: Backgrounds apropriados para cada tema</li>
                </ul>
            </div>
            
            <div id="debug-output" class="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded hidden">
                <h3 class="font-semibold mb-2">Debug Output:</h3>
                <pre id="debug-text" class="text-xs"></pre>
            </div>
        </div>
    </div>
    
    <script>
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            console.log('Dark mode toggled. Current classes:', document.documentElement.className);
        }
        
        function debugStyles() {
            const output = document.getElementById('debug-output');
            const debugText = document.getElementById('debug-text');
            
            const teste1 = document.querySelector('div[class*="bg-white dark:bg-black"]:first-of-type');
            const teste2 = document.querySelector('div[class*="bg-white dark:bg-black"]:nth-of-type(2)');
            const teste3 = document.querySelector('div[class*="bg-white dark:bg-black"]:nth-of-type(3)');
            
            const debug = {
                htmlClasses: document.documentElement.className,
                bodyComputedBg: getComputedStyle(document.body).backgroundColor,
                teste1: teste1 ? {
                    classes: teste1.className,
                    computedBg: getComputedStyle(teste1).backgroundColor
                } : 'not found',
                teste2: teste2 ? {
                    classes: teste2.className,
                    computedBg: getComputedStyle(teste2).backgroundColor
                } : 'not found',
                teste3: teste3 ? {
                    classes: teste3.className,
                    computedBg: getComputedStyle(teste3).backgroundColor
                } : 'not found',
                cssVariables: {
                    bgPrimary: getComputedStyle(document.documentElement).getPropertyValue('--bg-primary'),
                    bgSecondary: getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary'),
                    textPrimary: getComputedStyle(document.documentElement).getPropertyValue('--text-primary')
                }
            };
            
            debugText.textContent = JSON.stringify(debug, null, 2);
            output.classList.remove('hidden');
            
            console.log('Debug info:', debug);
        }
        
        // Auto debug on load
        window.addEventListener('load', () => {
            console.log('Page loaded. Testing profile background fixes...');
            debugStyles();
        });
    </script>
</body>
</html>
