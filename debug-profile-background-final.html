<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Profile Background - Final Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Simular EXATAMENTE o CSS do projeto */
        :root {
            --color-white: #ffffff;
            --color-black: #000000;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --border-primary: #e2e8f0;
            color-scheme: light;
            color: #1e293b;
        }
        
        .dark {
            --bg-primary: #000000;
            --bg-secondary: #0a0a0a;
            --bg-tertiary: #171717;
            --text-primary: rgba(255, 255, 255, 0.95);
            --text-secondary: rgba(255, 255, 255, 0.7);
            --border-primary: #262626;
            color-scheme: dark;
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* Simular as correções aplicadas */
        html {
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: #f8fafc !important;
        }
        
        .dark html {
            background-color: #0a0a0a !important;
        }
        
        body {
            background-color: #f8fafc !important;
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        
        .dark body {
            background-color: #0a0a0a !important;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .card-primary {
            background-color: white !important;
            border-color: var(--border-primary);
        }
        
        .dark .card-primary {
            background-color: #1a1a1a !important;
            border-color: var(--border-primary);
        }
        
        .sidebar-container {
            background-color: white !important;
            border-color: var(--border-primary);
        }
        
        .dark .sidebar-container {
            background-color: black !important;
        }
    </style>
</head>
<body>
    <div class="min-h-screen">
        <!-- Simular PageTemplate -->
        <div class="min-h-screen bg-white dark:bg-black">
            <!-- Header sticky -->
            <div class="sticky top-0 z-50 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800 p-4">
                <h1 class="text-xl font-bold text-gray-900 dark:text-white">Profile Page</h1>
            </div>
            
            <!-- Conteúdo principal -->
            <main class="page-template-content px-4 pt-8 pb-8 max-w-page mx-auto">
                <div class="space-y-8">
                    <!-- Profile Header -->
                    <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6">
                        <!-- Avatar -->
                        <div class="flex-shrink-0">
                            <div class="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
                                <span class="text-gray-500 dark:text-gray-400 text-sm">Avatar</span>
                            </div>
                        </div>
                        
                        <!-- Profile Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                                        John Doe
                                    </h1>
                                    <p class="text-gray-500 dark:text-gray-400 text-lg">@johndoe</p>
                                </div>
                                
                                <!-- Follow button -->
                                <button class="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 px-6 py-2 rounded-lg font-medium transition-colors">
                                    Following
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tabs -->
                    <div class="border-b border-gray-200 dark:border-gray-800">
                        <nav class="flex space-x-8">
                            <button class="py-3 px-1 border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 font-medium text-sm">
                                Boards
                            </button>
                            <button class="py-3 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white font-medium text-sm">
                                Pins
                            </button>
                        </nav>
                    </div>
                    
                    <!-- Tab Content -->
                    <div class="min-h-[400px]">
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            <!-- Board cards -->
                            <div class="card-primary rounded-lg shadow-sm border hover:shadow-lg transition-all duration-300 cursor-pointer p-4">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    My Board 1
                                </h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    5 pins
                                </p>
                            </div>
                            
                            <div class="card-primary rounded-lg shadow-sm border hover:shadow-lg transition-all duration-300 cursor-pointer p-4">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    My Board 2
                                </h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    12 pins
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <div class="fixed bottom-4 right-4 space-x-2">
        <button onclick="toggleDarkMode()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Toggle Dark Mode
        </button>
        
        <button onclick="debugStyles()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
            Debug
        </button>
    </div>
    
    <div id="debug-output" class="fixed top-4 left-4 max-w-md p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-lg hidden">
        <h3 class="font-semibold mb-2 text-gray-900 dark:text-white">Debug Output:</h3>
        <pre id="debug-text" class="text-xs text-gray-700 dark:text-gray-300 overflow-auto max-h-96"></pre>
        <button onclick="closeDebug()" class="mt-2 text-sm text-blue-600 hover:text-blue-800">Close</button>
    </div>
    
    <script>
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            console.log('Dark mode toggled. Current classes:', document.documentElement.className);
        }
        
        function debugStyles() {
            const output = document.getElementById('debug-output');
            const debugText = document.getElementById('debug-text');
            
            const pageTemplate = document.querySelector('.min-h-screen.bg-white');
            const body = document.body;
            const html = document.documentElement;
            const cards = document.querySelectorAll('.card-primary');
            
            const debug = {
                htmlClasses: html.className,
                htmlComputedBg: getComputedStyle(html).backgroundColor,
                bodyComputedBg: getComputedStyle(body).backgroundColor,
                pageTemplate: pageTemplate ? {
                    classes: pageTemplate.className,
                    computedBg: getComputedStyle(pageTemplate).backgroundColor
                } : 'not found',
                cards: Array.from(cards).map((card, index) => ({
                    index,
                    classes: card.className,
                    computedBg: getComputedStyle(card).backgroundColor
                })),
                cssVariables: {
                    bgPrimary: getComputedStyle(html).getPropertyValue('--bg-primary'),
                    bgSecondary: getComputedStyle(html).getPropertyValue('--bg-secondary'),
                    textPrimary: getComputedStyle(html).getPropertyValue('--text-primary'),
                    borderPrimary: getComputedStyle(html).getPropertyValue('--border-primary')
                }
            };
            
            debugText.textContent = JSON.stringify(debug, null, 2);
            output.classList.remove('hidden');
            
            console.log('Debug info:', debug);
        }
        
        function closeDebug() {
            document.getElementById('debug-output').classList.add('hidden');
        }
        
        // Auto debug on load
        window.addEventListener('load', () => {
            console.log('Page loaded. Testing final profile background fixes...');
            debugStyles();
        });
    </script>
</body>
</html>
