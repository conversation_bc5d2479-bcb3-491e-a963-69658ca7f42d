<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 FCM Test - No Hang Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
        }

        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .result {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }

        .success {
            color: #51cf66;
        }

        .error {
            color: #ff6b6b;
        }

        .warning {
            color: #ffd43b;
        }

        .info {
            color: #74c0fc;
        }

        .timeout-indicator {
            display: none;
            background: #ff9800;
            color: white;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>🚀 FCM Test - No Hang Version</h1>
        <p>Robust FCM testing with timeout protection</p>
    </div>

    <div class="test-section">
        <h3>🛠️ Preparação</h3>
        <button onclick="clearAllCaches()">🗑️ Clear All Caches</button>
        <button onclick="unregisterAllSW()">🔄 Unregister All Service Workers</button>
        <button onclick="checkBrowserSupport()">🔍 Check Browser Support</button>
    </div>

    <div class="test-section">
        <h3>🧪 FCM Test</h3>
        <button onclick="runRobustFCMTest()" id="testBtn">🚀 Run Robust FCM Test</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>

        <div class="timeout-indicator" id="timeoutIndicator">
            ⏱️ Timeout protection active - will abort if step takes too long
        </div>
    </div>

    <div class="test-section">
        <h3>📊 Test Results</h3>
        <div id="result" class="result">Click "Run Robust FCM Test" to start...</div>
    </div>

    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        let testRunning = false;
        let currentTimeout = null;

        function log(message, type = 'info') {
            const element = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `<span class="${type}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearResults() {
            document.getElementById('result').innerHTML = 'Results cleared. Ready for new test...\n';
        }

        function showTimeoutIndicator(show = true) {
            document.getElementById('timeoutIndicator').style.display = show ? 'block' : 'none';
        }

        function setTestButtonState(running) {
            const btn = document.getElementById('testBtn');
            btn.disabled = running;
            btn.textContent = running ? '🔄 Testing...' : '🚀 Run Robust FCM Test';
            testRunning = running;
        }

        // Função para criar timeout com abort
        function withTimeout(promise, timeoutMs, stepName) {
            return new Promise((resolve, reject) => {
                const timeoutId = setTimeout(() => {
                    log(`⏱️ TIMEOUT: ${stepName} took longer than ${timeoutMs}ms`, 'warning');
                    reject(new Error(`Timeout: ${stepName}`));
                }, timeoutMs);

                currentTimeout = timeoutId;

                promise
                    .then((result) => {
                        clearTimeout(timeoutId);
                        resolve(result);
                    })
                    .catch((error) => {
                        clearTimeout(timeoutId);
                        reject(error);
                    });
            });
        }

        async function clearAllCaches() {
            try {
                log('🗑️ Clearing all caches...', 'info');

                // Clear service worker caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    log(`✅ Cleared ${cacheNames.length} cache(s)`, 'success');
                }

                // Clear localStorage
                localStorage.clear();
                log('✅ Cleared localStorage', 'success');

                // Clear sessionStorage
                sessionStorage.clear();
                log('✅ Cleared sessionStorage', 'success');

            } catch (error) {
                log(`❌ Cache clearing failed: ${error.message}`, 'error');
            }
        }

        async function unregisterAllSW() {
            try {
                log('🔄 Unregistering all service workers...', 'info');

                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();

                    for (let registration of registrations) {
                        await registration.unregister();
                        log(`✅ Unregistered SW: ${registration.scope}`, 'success');
                    }

                    if (registrations.length === 0) {
                        log('ℹ️ No service workers to unregister', 'info');
                    }
                } else {
                    log('❌ Service Worker not supported', 'error');
                }

            } catch (error) {
                log(`❌ SW unregistration failed: ${error.message}`, 'error');
            }
        }

        async function checkBrowserSupport() {
            log('🔍 Checking browser support...', 'info');

            const checks = [
                { name: 'Service Worker', supported: 'serviceWorker' in navigator },
                { name: 'Notifications', supported: 'Notification' in window },
                { name: 'Push Manager', supported: 'PushManager' in window },
                { name: 'Firebase', supported: typeof firebase !== 'undefined' }
            ];

            checks.forEach(check => {
                const status = check.supported ? '✅' : '❌';
                const color = check.supported ? 'success' : 'error';
                log(`${status} ${check.name}: ${check.supported ? 'Supported' : 'Not supported'}`, color);
            });
        }

        async function runRobustFCMTest() {
            if (testRunning) return;

            setTestButtonState(true);
            showTimeoutIndicator(true);
            clearResults();

            try {
                log('🚀 Starting robust FCM test with timeout protection...', 'info');

                // Step 1: Load configuration (5s timeout)
                log('🔧 Step 1: Loading Firebase configuration...', 'info');
                const configData = await withTimeout(
                    fetch('/api/notifications/fcm/config').then(r => r.json()),
                    5000,
                    'Configuration loading'
                );

                if (!configData.success) {
                    throw new Error('Failed to load configuration');
                }

                const firebaseConfig = configData.config;
                log('✅ Configuration loaded successfully', 'success');

                // Step 2: Initialize Firebase (3s timeout)
                log('🔥 Step 2: Initializing Firebase...', 'info');
                await withTimeout(
                    new Promise((resolve) => {
                        // Use default app name so firebase.messaging() can find it
                        let app;
                        if (firebase.apps && firebase.apps.length > 0) {
                            app = firebase.app(); // Reuse existing default app
                        } else {
                            app = firebase.initializeApp(firebaseConfig); // Create DEFAULT app
                        }
                        resolve(app);
                    }),
                    3000,
                    'Firebase initialization'
                );

                log('✅ Firebase initialized', 'success');

                // Step 3: Register service worker (10s timeout)
                log('🔧 Step 3: Registering service worker...', 'info');
                const registration = await withTimeout(
                    navigator.serviceWorker.register('/firebase-messaging-sw-robust.js'),
                    10000,
                    'Service worker registration'
                );

                log('✅ Service worker registered', 'success');
                log(`📋 Scope: ${registration.scope}`, 'info');

                // Step 4: Wait for service worker ready (8s timeout)
                log('⏳ Step 4: Waiting for service worker ready...', 'info');
                await withTimeout(
                    navigator.serviceWorker.ready,
                    8000,
                    'Service worker ready'
                );

                log('✅ Service worker is ready', 'success');

                // Step 5: Request permission (5s timeout)
                log('🔔 Step 5: Requesting notification permission...', 'info');
                const permission = await withTimeout(
                    Notification.requestPermission(),
                    5000,
                    'Permission request'
                );

                if (permission !== 'granted') {
                    throw new Error(`Permission denied: ${permission}`);
                }

                log('✅ Permission granted', 'success');

                // Step 6: Generate FCM token (15s timeout)
                log('🔑 Step 6: Generating FCM token...', 'info');
                const messaging = firebase.messaging();
                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";

                const token = await withTimeout(
                    messaging.getToken({ vapidKey: vapidKey, serviceWorkerRegistration: registration }),
                    15000,
                    'FCM token generation'
                );

                if (token) {
                    log('✅ FCM token generated successfully!', 'success');
                    log(`🎫 Token: ${token.substring(0, 50)}...`, 'success');
                    log(`📏 Token length: ${token.length} characters`, 'info');

                    log('', 'info');
                    log('🎉🎉🎉 ALL TESTS PASSED! 🎉🎉🎉', 'success');
                    log('✅ FCM is fully functional!', 'success');
                } else {
                    throw new Error('No FCM token received');
                }

            } catch (error) {
                log('❌ Test failed: ' + error.message, 'error');

                if (error.message.includes('Timeout')) {
                    log('💡 Timeout occurred - try clearing caches and running again', 'warning');
                    log('💡 Or check browser console for more details', 'warning');
                }

                if (error.code) {
                    log('🔍 Error code: ' + error.code, 'error');
                }

                if (error.customData) {
                    log('🔍 Error details: ' + JSON.stringify(error.customData), 'error');
                }
            } finally {
                setTestButtonState(false);
                showTimeoutIndicator(false);

                if (currentTimeout) {
                    clearTimeout(currentTimeout);
                    currentTimeout = null;
                }
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (currentTimeout) {
                clearTimeout(currentTimeout);
            }
        });
    </script>
</body>

</html>