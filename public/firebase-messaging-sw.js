// Firebase Messaging Service Worker
// This service worker enables background notifications

console.log('[firebase-messaging-sw.js] Service worker starting...');

// Import Firebase scripts using importScripts (required for service workers)
try {
    importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
    importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');
    console.log('[firebase-messaging-sw.js] Firebase scripts loaded successfully');
} catch (error) {
    console.error('[firebase-messaging-sw.js] Error loading Firebase scripts:', error);
}

// Firebase configuration - Note: Service workers can't access import.meta.env
// These values should match your main app configuration
const firebaseConfig = {
  apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
  authDomain: "iconpal-cf925.firebaseapp.com",
  projectId: "iconpal-cf925",
  storageBucket: "iconpal-cf925.appspot.com",
  messagingSenderId: "887109976546",
  appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
};

// Initialize Firebase app with error handling
let messaging = null;
try {
    if (typeof firebase !== 'undefined') {
        firebase.initializeApp(firebaseConfig);
        messaging = firebase.messaging();
        console.log('[firebase-messaging-sw.js] Firebase initialized successfully');
    } else {
        console.error('[firebase-messaging-sw.js] Firebase not available');
    }
} catch (error) {
    console.error('[firebase-messaging-sw.js] Error initializing Firebase:', error);
}

// Handle background messages
if (messaging) {
    messaging.onBackgroundMessage((payload) => {
      console.log('[firebase-messaging-sw.js] Received background message:', payload);

      const notificationTitle = payload.notification?.title || 'PinPal Notification';
      const notificationOptions = {
        body: payload.notification?.body || 'You have a new notification',
        icon: '/pinpal-logo-icon.png',
        badge: '/pinpal-logo-icon.png',
        tag: 'pinpal-notification',
        requireInteraction: true,
        actions: [
          {
            action: 'open',
            title: 'Open PinPal'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ],
        data: payload.data || {}
      };

      // Show notification
      return self.registration.showNotification(notificationTitle, notificationOptions);
    });
}

// Handle notification click
self.addEventListener('notificationclick', function(event) {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Open or focus the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clientList) {
      // Check if app is already open
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes('localhost:5773') || client.url.includes('pinpal.com')) {
          return client.focus();
        }
      }
      
      // If app is not open, open it
      return clients.openWindow('http://localhost:5773');
    }).catch(function(error) {
      console.error('[firebase-messaging-sw.js] Error handling notification click:', error);
    })
  );
});

// Handle message events (to prevent listener errors)
self.addEventListener('message', function(event) {
  console.log('[firebase-messaging-sw.js] Message received:', event.data);
  
  // Always respond to prevent async listener errors
  if (event.ports && event.ports[0]) {
    event.ports[0].postMessage({ success: true });
  }
});

// Handle push events
self.addEventListener('push', function(event) {
  console.log('[firebase-messaging-sw.js] Push event received');
  
  if (event.data) {
    try {
      const payload = event.data.json();
      console.log('[firebase-messaging-sw.js] Push payload:', payload);
      
      const notificationTitle = payload.notification?.title || 'PinPal Notification';
      const notificationOptions = {
        body: payload.notification?.body || 'You have a new notification',
        icon: '/pinpal-logo-icon.png',
        badge: '/pinpal-logo-icon.png',
        tag: 'pinpal-notification',
        requireInteraction: true,
        data: payload.data || {}
      };
      
      event.waitUntil(
        self.registration.showNotification(notificationTitle, notificationOptions)
      );
    } catch (error) {
      console.error('[firebase-messaging-sw.js] Error parsing push data:', error);
    }
  }
});

// Service worker installation
self.addEventListener('install', function(event) {
  console.log('[firebase-messaging-sw.js] Service worker installing...');
  self.skipWaiting();
});

// Service worker activation
self.addEventListener('activate', function(event) {
  console.log('[firebase-messaging-sw.js] Service worker activating...');
  event.waitUntil(self.clients.claim());
});

console.log('[firebase-messaging-sw.js] Service worker setup complete'); 