<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple FCM Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .warning { color: #ffd43b; }
    </style>
</head>
<body>
    <h1>🔥 Simple FCM Test</h1>
    
    <div>
        <button onclick="testWithMinimalServiceWorker()">�� Test with Minimal Service Worker</button>
        <button onclick="clearServiceWorkers()">🗑️ Clear Service Workers</button>
    </div>
    
    <div id="result" class="result">Click a button to start testing...</div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        function log(message, type = 'info') {
            const element = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += '<span class="' + className + '">[' + timestamp + '] ' + message + '</span>\n';
            element.scrollTop = element.scrollHeight;
        }

        function clear() {
            document.getElementById('result').innerHTML = '';
        }

        async function clearServiceWorkers() {
            clear();
            log('🗑️ Clearing all service workers...', 'info');
            
            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    log('📋 Found ' + registrations.length + ' service worker(s)', 'info');
                    
                    for (const registration of registrations) {
                        log('🗑️ Unregistering SW: ' + registration.scope, 'info');
                        await registration.unregister();
                    }
                    
                    log('✅ All service workers cleared', 'success');
                } else {
                    log('❌ Service workers not supported', 'error');
                }
            } catch (error) {
                log('❌ Error clearing service workers: ' + error.message, 'error');
            }
        }

        async function testWithMinimalServiceWorker() {
            clear();
            log('🎫 Testing FCM with minimal service worker...', 'info');
            
            try {
                // Request permission first
                if (Notification.permission !== 'granted') {
                    log('📋 Requesting notification permission...', 'info');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('❌ Permission denied', 'error');
                        return;
                    }
                }
                log('✅ Permission granted', 'success');

                // Register minimal service worker
                if (!('serviceWorker' in navigator)) {
                    log('❌ Service workers not supported', 'error');
                    return;
                }

                log('🔧 Registering minimal service worker...', 'info');
                const swReg = await navigator.serviceWorker.register('/firebase-messaging-sw-minimal.js');
                log('✅ Service worker registered: ' + swReg.scope, 'success');

                // Wait for service worker to be ready
                await navigator.serviceWorker.ready;
                log('✅ Service worker is ready', 'success');

                // Initialize Firebase
                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                log('🔥 Initializing Firebase...', 'info');
                const app = firebase.initializeApp(firebaseConfig, 'test-minimal-sw-' + Date.now());
                const messaging = firebase.messaging(app);
                log('✅ Firebase initialized', 'success');

                // Get FCM token
                log('🔑 Getting FCM token...', 'info');
                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";
                
                const token = await messaging.getToken({
                    vapidKey: vapidKey,
                    serviceWorkerRegistration: swReg
                });

                if (token) {
                    log('🎉 FCM Token generated successfully!', 'success');
                    log('📏 Token length: ' + token.length + ' characters', 'success');
                    log('🔑 Token preview: ' + token.substring(0, 50) + '...', 'success');
                    
                    // Copy to clipboard
                    try {
                        await navigator.clipboard.writeText(token);
                        log('📋 Token copied to clipboard!', 'success');
                    } catch (e) {
                        log('⚠️ Could not copy to clipboard', 'warning');
                    }
                } else {
                    log('❌ No token received', 'error');
                }
                
            } catch (error) {
                log('❌ FCM test failed: ' + error.message, 'error');
                log('🔍 Error code: ' + (error.code || 'unknown'), 'error');
                log('🔍 Error details: ' + JSON.stringify(error), 'error');
                console.error('FCM test error:', error);
            }
        }
    </script>
</body>
</html>
