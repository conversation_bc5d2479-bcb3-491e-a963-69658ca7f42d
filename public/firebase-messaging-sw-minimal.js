// Minimal Firebase Messaging Service Worker for testing
console.log('[firebase-messaging-sw-minimal.js] Starting minimal service worker...');

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
  authDomain: "iconpal-cf925.firebaseapp.com",
  projectId: "iconpal-cf925",
  storageBucket: "iconpal-cf925.appspot.com",
  messagingSenderId: "887109976546",
  appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Simple background message handler
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw-minimal.js] Background message:', payload);
});

// Service worker events
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw-minimal.js] Installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw-minimal.js] Activating...');
  event.waitUntil(self.clients.claim());
});

console.log('[firebase-messaging-sw-minimal.js] Minimal service worker ready');
