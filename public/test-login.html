<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Login Simples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }

        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
        }

        input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #555;
            border-radius: 5px;
            background: #333;
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }

        button {
            width: 100%;
            padding: 15px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px 0;
        }

        button:hover {
            background: #0b7dda;
        }

        .signup-btn {
            background: #4CAF50;
        }

        .signup-btn:hover {
            background: #45a049;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .success {
            background: #1b4332;
            color: #b7e4c7;
        }

        .error {
            background: #590e0e;
            color: #ffb3ba;
        }

        .users-list {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-size: 14px;
        }

        .user-item {
            margin: 5px 0;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
        }

        .user-item:hover {
            background: #555;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔐 Teste Login Direto</h1>

        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="TestPass123!">

        <button onclick="testLogin()">🔐 Fazer Login</button>
        <button onclick="showSignupForm()" class="signup-btn">➕ Criar Nova Conta</button>

        <div class="users-list">
            <h3>👥 Usuários de Teste Disponíveis:</h3>
            <div class="user-item" onclick="fillLogin('<EMAIL>', 'TestPass123!')">
                📧 <EMAIL>
            </div>
            <div class="user-item" onclick="fillLogin('<EMAIL>', 'TestPass123!')">
                📧 <EMAIL>
            </div>
            <div class="user-item" onclick="fillLogin('<EMAIL>', 'TestPass123!')">
                📧 <EMAIL>
            </div>
            <div class="user-item" onclick="fillLogin('<EMAIL>', 'TestPass123!')">
                📧 <EMAIL>
            </div>
            <div class="user-item" onclick="fillLogin('<EMAIL>', 'TestPass123!')">
                📧 <EMAIL>
            </div>
        </div>

        <div id="result" class="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
        import {
            getAuth,
            signInWithEmailAndPassword,
            onAuthStateChanged,
            signOut
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
            authDomain: "iconpal-cf925.firebaseapp.com",
            projectId: "iconpal-cf925",
            storageBucket: "iconpal-cf925.appspot.com",
            messagingSenderId: "887109976546",
            appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        window.auth = auth;
        window.signInWithEmailAndPassword = signInWithEmailAndPassword;
        window.signOut = signOut;

        function log(message, type = 'success') {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            console.log(message);
        }

        window.log = log;

        // Monitor auth state
        onAuthStateChanged(auth, (user) => {
            if (user) {
                log(`✅ Usuário logado:
UID: ${user.uid}
Email: ${user.email}
Nome: ${user.displayName || 'Não definido'}

🌐 Você pode acessar o site principal:
http://localhost:5773

🚪 Clique aqui para fazer logout`, 'success');

                // Add logout functionality
                document.getElementById('result').onclick = async () => {
                    try {
                        await window.signOut(auth);
                        log('👋 Logout realizado com sucesso!', 'success');
                        document.getElementById('result').onclick = null;
                    } catch (error) {
                        log(`❌ Erro no logout: ${error.message}`, 'error');
                    }
                };
            } else {
                log('Firebase inicializado! Pronto para login.', 'success');
                document.getElementById('result').onclick = null;
            }
        });
    </script>

    <script>
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!email || !password) {
                log('❌ Preencha email e senha', 'error');
                return;
            }

            try {
                log('🔄 Fazendo login...', 'success');

                const userCredential = await window.signInWithEmailAndPassword(window.auth, email, password);
                const user = userCredential.user;

                log(`🎉 LOGIN REALIZADO COM SUCESSO!

✅ Firebase Auth: OK
UID: ${user.uid}
Email: ${user.email}
Nome: ${user.displayName || 'Não definido'}

🌐 Acesse o site principal:
http://localhost:5773

🚪 Clique aqui para fazer logout`, 'success');

            } catch (error) {
                let errorMessage = `❌ ERRO NO LOGIN:
Código: ${error.code}
Mensagem: ${error.message}`;

                if (error.code === 'auth/user-not-found') {
                    errorMessage += `

💡 SOLUÇÃO: Usuário não encontrado.
Verifique o email ou crie uma conta.`;
                } else if (error.code === 'auth/wrong-password') {
                    errorMessage += `

💡 SOLUÇÃO: Senha incorreta.
Verifique a senha ou redefina.`;
                } else if (error.code === 'auth/invalid-email') {
                    errorMessage += `

💡 SOLUÇÃO: Email inválido.
Verifique o formato do email.`;
                } else if (error.code === 'auth/too-many-requests') {
                    errorMessage += `

💡 SOLUÇÃO: Muitas tentativas.
Aguarde alguns minutos.`;
                }

                log(errorMessage, 'error');
            }
        }

        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            log(`📝 Campos preenchidos com: ${email}
Clique em "Fazer Login" para testar.`, 'success');
        }

        function showSignupForm() {
            window.open('/test-signup.html', '_blank');
        }

        // Enter key support
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                testLogin();
            }
        });
    </script>
</body>

</html>