<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 FCM Final Test - PinPal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #51cf66; }
        .error { color: #ff6b6b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 FCM Final Test - PinPal</h1>
        <p>Complete Firebase Cloud Messaging validation</p>
    </div>

    <div class="test-section">
        <h3>🚀 Actions</h3>
        <button onclick="runCompleteTest()">🧪 Run Complete FCM Test</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div class="test-section">
        <h3>📊 Test Results</h3>
        <div id="result" class="result">Click "Run Complete FCM Test" to start...</div>
    </div>

    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        function log(message, type = 'info') {
            const element = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += '<span class="' + type + '">[' + timestamp + '] ' + message + '</span>\n';
            element.scrollTop = element.scrollHeight;
        }

        function clearResults() {
            document.getElementById('result').innerHTML = 'Results cleared. Ready for new test...\n';
        }

        async function runCompleteTest() {
            clearResults();
            log('🚀 Starting complete FCM validation test...', 'info');
            
            try {
                // Step 1: Load configuration
                log('🔧 Loading Firebase configuration...', 'info');
                const configResponse = await fetch('/api/notifications/fcm/config');
                const configData = await configResponse.json();
                
                if (!configData.success) {
                    throw new Error('Failed to load configuration');
                }
                
                const firebaseConfig = configData.config;
                log('✅ Configuration loaded: ' + firebaseConfig.apiKey, 'success');
                
                // Step 2: Initialize Firebase
                log('🔥 Initializing Firebase...', 'info');
                const app = firebase.initializeApp(firebaseConfig, 'test-' + Date.now());
                const messaging = firebase.messaging(app);
                log('✅ Firebase initialized', 'success');
                
                // Step 3: Register service worker
                log('🔧 Registering service worker...', 'info');
                await navigator.serviceWorker.register('/firebase-messaging-sw-minimal.js');
                await navigator.serviceWorker.ready;
                log('✅ Service worker ready', 'success');
                
                // Step 4: Request permission
                log('🔔 Requesting permission...', 'info');
                const permission = await Notification.requestPermission();
                if (permission !== 'granted') {
                    throw new Error('Permission denied: ' + permission);
                }
                log('✅ Permission granted', 'success');
                
                // Step 5: Generate FCM token
                log('�� Generating FCM token...', 'info');
                const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";
                const token = await messaging.getToken({ vapidKey: vapidKey });
                
                if (token) {
                    log('✅ FCM token generated successfully!', 'success');
                    log('🎫 Token: ' + token.substring(0, 50) + '...', 'success');
                    log('', 'info');
                    log('🎉🎉🎉 ALL TESTS PASSED! 🎉🎉🎉', 'success');
                    log('✅ FCM is fully configured and working!', 'success');
                } else {
                    throw new Error('No FCM token received');
                }
                
            } catch (error) {
                log('❌ Test failed: ' + error.message, 'error');
                log('🔍 Error code: ' + (error.code || 'unknown'), 'error');
                if (error.customData) {
                    log('🔍 Details: ' + JSON.stringify(error.customData), 'error');
                }
            }
        }
    </script>
</body>
</html>
