<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test FCM REST API v1</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }

        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .result {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }

        .success {
            color: #51cf66;
        }

        .error {
            color: #ff6b6b;
        }

        .warning {
            color: #ffd43b;
        }

        .info {
            color: #74c0fc;
        }

        .token-display {
            background: #000;
            padding: 10px;
            border-radius: 4px;
            color: #00ff00;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <h1>🚀 FCM REST API v1 Test</h1>

    <div class="container">
        <h2>📋 Documentation</h2>
        <p>Testing implementation based on:
            <a href="https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages" target="_blank"
                style="color: #60a5fa;">
                FCM REST API v1 Official Documentation
            </a>
        </p>
    </div>

    <div class="container">
        <h2>Step 1: Get FCM Token</h2>
        <button onclick="getFCMToken()">🎫 Generate FCM Token</button>
        <button onclick="clearServiceWorkers()" style="background: #ff6b6b;">🗑️ Clear Service Workers</button>
        <div id="tokenResult" class="result">Click button to generate FCM token...</div>
        <div id="tokenDisplay" class="token-display" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>Step 2: Test FCM API v1</h2>
        <button onclick="testFCMStatus()" disabled id="statusBtn">📊 Check FCM Status</button>
        <button onclick="validateToken()" disabled id="validateBtn">🔍 Validate Token</button>
        <button onclick="sendTestNotification()" disabled id="testBtn">📱 Send Test Notification</button>
        <div id="apiResult" class="result">Generate FCM token first...</div>
    </div>

    <div class="container">
        <h2>Step 3: Custom Notification</h2>
        <div style="margin: 10px 0;">
            <input type="text" id="customTitle" placeholder="Notification Title"
                style="width: 100%; padding: 8px; margin: 5px 0; background: #333; color: white; border: 1px solid #555; border-radius: 4px;">
            <textarea id="customBody" placeholder="Notification Body" rows="3"
                style="width: 100%; padding: 8px; margin: 5px 0; background: #333; color: white; border: 1px solid #555; border-radius: 4px;"></textarea>
        </div>
        <button onclick="sendCustomNotification()" disabled id="customBtn">🎨 Send Custom Notification</button>
        <div id="customResult" class="result">Configure custom notification above...</div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        let currentToken = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function clearServiceWorkers() {
            clearLog('tokenResult');
            log('tokenResult', '🗑️ Clearing all service workers...', 'info');

            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    log('tokenResult', `📋 Found ${registrations.length} service worker(s)`, 'info');

                    for (const registration of registrations) {
                        log('tokenResult', `🗑️ Unregistering SW: ${registration.scope}`, 'info');
                        await registration.unregister();
                    }

                    log('tokenResult', '✅ All service workers cleared', 'success');
                    log('tokenResult', '🔄 You can now try generating FCM token again', 'info');
                } else {
                    log('tokenResult', '❌ Service workers not supported', 'error');
                }
            } catch (error) {
                log('tokenResult', '❌ Error clearing service workers: ' + error.message, 'error');
            }
        }

        async function getFCMToken() {
            clearLog('tokenResult');
            log('tokenResult', '🎫 Generating FCM token...', 'info');

            try {
                // Initial service worker state check
                if ('serviceWorker' in navigator) {
                    log('tokenResult', '🔍 Checking existing service workers...', 'info');
                    const existingRegistrations = await navigator.serviceWorker.getRegistrations();
                    log('tokenResult', `📋 Found ${existingRegistrations.length} existing service worker(s)`, 'info');

                    for (const reg of existingRegistrations) {
                        log('tokenResult', `📋 Existing SW: ${reg.scope} (${reg.active ? 'active' : 'inactive'})`, 'info');
                    }
                } else {
                    log('tokenResult', '❌ Service workers not supported', 'error');
                    return;
                }

                // Check permission
                if (Notification.permission !== 'granted') {
                    log('tokenResult', '📋 Requesting notification permission...', 'info');
                    const permission = await Notification.requestPermission();
                    if (permission !== 'granted') {
                        log('tokenResult', '❌ Permission denied', 'error');
                        return;
                    }
                }
                log('tokenResult', '✅ Permission granted', 'success');

                // ✅ Registrar o service-worker do Firebase Messaging
                if (!('serviceWorker' in navigator)) {
                    log('tokenResult', '❌ Service workers are not supported in this browser', 'error');
                    return;
                }

                log('tokenResult', '🔧 Registering firebase-messaging-sw.js ...', 'info');

                // Add timeout to service worker registration
                const registrationPromise = navigator.serviceWorker.register('/firebase-messaging-sw.js', {
                    scope: '/'
                });

                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Service worker registration timeout after 10 seconds')), 10000);
                });

                let swReg;
                try {
                    swReg = await Promise.race([registrationPromise, timeoutPromise]);
                    log('tokenResult', '✅ Service worker registered successfully', 'success');
                    log('tokenResult', `📋 SW scope: ${swReg.scope}`, 'info');
                    log('tokenResult', `📋 SW state: ${swReg.installing ? 'installing' : swReg.waiting ? 'waiting' : swReg.active ? 'active' : 'unknown'}`, 'info');
                } catch (swError) {
                    log('tokenResult', '❌ Service worker registration failed: ' + swError.message, 'error');
                    log('tokenResult', '🔄 Attempting to continue without explicit registration...', 'warning');

                    // Try to get existing registration
                    const existingReg = await navigator.serviceWorker.getRegistration('/');
                    if (existingReg) {
                        swReg = existingReg;
                        log('tokenResult', '✅ Using existing service worker registration', 'success');
                    } else {
                        log('tokenResult', '❌ No service worker available, FCM may not work properly', 'error');
                        // Continue anyway - some browsers might still work
                    }
                }

                // Wait for service worker to be ready with timeout
                log('tokenResult', '⏳ Waiting for service worker to be ready...', 'info');
                const readyPromise = navigator.serviceWorker.ready;
                const readyTimeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Service worker ready timeout after 5 seconds')), 5000);
                });

                try {
                    await Promise.race([readyPromise, readyTimeoutPromise]);
                    log('tokenResult', '✅ Service worker is ready', 'success');
                } catch (readyError) {
                    log('tokenResult', '⚠️ Service worker ready timeout: ' + readyError.message, 'warning');
                    log('tokenResult', '🔄 Continuing with token generation...', 'info');
                }

                // Firebase configuration
                log('tokenResult', '🔥 Getting Firebase configuration from main app...', 'info');
                let firebaseConfig;

                try {
                    // Try to get configuration from the main application
                    const configResponse = await fetch('/api/notifications/fcm/config');
                    const configData = await configResponse.json();

                    if (configData.success && configData.config) {
                        firebaseConfig = configData.config;
                        log('tokenResult', '✅ Firebase config obtained from backend', 'success');
                    } else {
                        throw new Error('Backend config not available');
                    }
                } catch (configError) {
                    log('tokenResult', '⚠️ Using fallback Firebase config', 'warning');
                    // Fallback configuration
                    firebaseConfig = {
                        apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                        authDomain: "iconpal-cf925.firebaseapp.com",
                        projectId: "iconpal-cf925",
                        storageBucket: "iconpal-cf925.appspot.com",
                        messagingSenderId: "887109976546",
                        appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                    };
                }

                // ✅ CORRIGIDO: Buscar VAPID key do backend
                log('tokenResult', '🔑 Getting VAPID key from backend...', 'info');
                let vapidKey;
                try {
                    const vapidResponse = await fetch('/api/notifications/fcm/vapid-key');
                    const vapidData = await vapidResponse.json();
                    if (vapidData.success) {
                        vapidKey = vapidData.vapidKey;
                        log('tokenResult', '✅ VAPID key obtained from backend', 'success');
                        log('tokenResult', `🔑 VAPID key preview: ${vapidKey.substring(0, 20)}...`, 'info');
                    } else {
                        throw new Error('Failed to get VAPID key from backend: ' + vapidData.error);
                    }
                } catch (error) {
                    log('tokenResult', '⚠️ Error getting VAPID key from backend: ' + error.message, 'warning');
                    log('tokenResult', '🔄 Using fallback VAPID key', 'warning');
                    vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";
                }

                // Initialize Firebase
                log('tokenResult', '🔥 Initializing Firebase...', 'info');
                const app = firebase.initializeApp(firebaseConfig, 'fcm-v1-test-' + Date.now());
                const messaging = firebase.messaging(app);
                log('tokenResult', '✅ Firebase initialized', 'success');

                // Get token with detailed logging
                log('tokenResult', '🔑 Getting FCM token with VAPID key...', 'info');
                log('tokenResult', '⏳ This may take a few seconds...', 'info');

                try {
                    // Try with service worker registration first
                    let tokenOptions = {
                        vapidKey: vapidKey
                    };

                    if (swReg) {
                        tokenOptions.serviceWorkerRegistration = swReg;
                        log('tokenResult', '🔧 Using explicit service worker registration', 'info');
                    } else {
                        log('tokenResult', '🔧 Using default service worker registration', 'warning');
                    }

                    const token = await messaging.getToken(tokenOptions);

                    if (token) {
                        currentToken = token;
                        log('tokenResult', '🎉 FCM Token generated successfully!', 'success');
                        log('tokenResult', `📏 Token length: ${token.length} characters`, 'info');

                        // Display token
                        const tokenDisplay = document.getElementById('tokenDisplay');
                        tokenDisplay.textContent = token;
                        tokenDisplay.style.display = 'block';

                        // Enable API test buttons
                        document.getElementById('statusBtn').disabled = false;
                        document.getElementById('validateBtn').disabled = false;
                        document.getElementById('testBtn').disabled = false;
                        document.getElementById('customBtn').disabled = false;

                        // Copy to clipboard
                        try {
                            await navigator.clipboard.writeText(token);
                            log('tokenResult', '📋 Token copied to clipboard!', 'success');
                        } catch (e) {
                            log('tokenResult', '⚠️ Could not copy to clipboard', 'warning');
                        }

                    } else {
                        log('tokenResult', '❌ No token received from Firebase', 'error');
                        log('tokenResult', '🔍 Check console for detailed errors', 'info');
                    }
                } catch (tokenError) {
                    log('tokenResult', '❌ Error getting FCM token: ' + tokenError.message, 'error');
                    log('tokenResult', '🔍 Error details: ' + JSON.stringify(tokenError), 'error');
                    console.error('FCM Token Error Details:', tokenError);

                    // If token generation failed, try without explicit service worker
                    if (swReg) {
                        log('tokenResult', '🔄 Retrying without explicit service worker...', 'warning');
                        try {
                            const fallbackToken = await messaging.getToken({
                                vapidKey: vapidKey
                            });

                            if (fallbackToken) {
                                currentToken = fallbackToken;
                                log('tokenResult', '🎉 FCM Token generated with fallback method!', 'success');

                                // Display token
                                const tokenDisplay = document.getElementById('tokenDisplay');
                                tokenDisplay.textContent = fallbackToken;
                                tokenDisplay.style.display = 'block';

                                // Enable API test buttons
                                document.getElementById('statusBtn').disabled = false;
                                document.getElementById('validateBtn').disabled = false;
                                document.getElementById('testBtn').disabled = false;
                                document.getElementById('customBtn').disabled = false;
                            }
                        } catch (fallbackError) {
                            log('tokenResult', '❌ Fallback token generation also failed: ' + fallbackError.message, 'error');
                        }
                    }
                }

            } catch (error) {
                log('tokenResult', `❌ Error: ${error.message}`, 'error');
                console.error('FCM Token Error:', error);
            }
        }

        async function testFCMStatus() {
            clearLog('apiResult');
            log('apiResult', '📊 Checking FCM service status...', 'info');

            try {
                const response = await fetch('/api/notifications/fcm/status');
                const data = await response.json();

                if (data.success) {
                    log('apiResult', '✅ FCM Service Status: ' + data.status, 'success');
                    log('apiResult', '📋 API Version: ' + data.apiVersion, 'info');
                    log('apiResult', '🏗️ Project ID: ' + data.projectId, 'info');
                    log('apiResult', '🔑 Service Account: ' + data.serviceAccount,
                        data.serviceAccount === 'configured' ? 'success' : 'warning');
                } else {
                    log('apiResult', '❌ FCM Status Error: ' + data.error, 'error');
                }

            } catch (error) {
                log('apiResult', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function validateToken() {
            if (!currentToken) {
                log('apiResult', '❌ No FCM token available', 'error');
                return;
            }

            clearLog('apiResult');
            log('apiResult', '🔍 Validating FCM token...', 'info');

            try {
                const response = await fetch('/api/notifications/fcm/validate-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: currentToken })
                });

                const data = await response.json();

                if (data.success) {
                    log('apiResult', `📋 Token validation: ${data.valid ? 'VALID' : 'INVALID'}`,
                        data.valid ? 'success' : 'error');
                    log('apiResult', `🎯 Token preview: ${data.token}`, 'info');
                } else {
                    log('apiResult', '❌ Validation error: ' + data.error, 'error');
                }

            } catch (error) {
                log('apiResult', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function sendTestNotification() {
            if (!currentToken) {
                log('apiResult', '❌ No FCM token available', 'error');
                return;
            }

            clearLog('apiResult');
            log('apiResult', '📱 Sending test notification via FCM API v1...', 'info');

            try {
                const response = await fetch('/api/notifications/fcm/send-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: currentToken })
                });

                const data = await response.json();

                if (data.success) {
                    log('apiResult', '🎉 Test notification sent successfully!', 'success');
                    log('apiResult', '📋 Message ID: ' + data.messageId, 'info');
                    log('apiResult', '⏰ Timestamp: ' + data.timestamp, 'info');
                    log('apiResult', '👀 Check your screen for the notification!', 'info');
                } else {
                    log('apiResult', '❌ Failed to send notification: ' + data.error, 'error');
                }

            } catch (error) {
                log('apiResult', `❌ Network error: ${error.message}`, 'error');
            }
        }

        async function sendCustomNotification() {
            if (!currentToken) {
                log('customResult', '❌ No FCM token available', 'error');
                return;
            }

            const title = document.getElementById('customTitle').value.trim();
            const body = document.getElementById('customBody').value.trim();

            if (!title || !body) {
                log('customResult', '❌ Please enter both title and body', 'error');
                return;
            }

            clearLog('customResult');
            log('customResult', '🎨 Sending custom notification...', 'info');
            log('customResult', `📋 Title: ${title}`, 'info');
            log('customResult', `📋 Body: ${body}`, 'info');

            try {
                const response = await fetch('/api/notifications/fcm/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: currentToken,
                        notification: {
                            title: title,
                            body: body
                        },
                        data: {
                            type: 'custom',
                            source: 'fcm-v1-test'
                        },
                        webpush: {
                            clickUrl: 'http://localhost:5773',
                            tag: 'pinpal-custom'
                        }
                    })
                });

                const data = await response.json();

                if (data.success) {
                    log('customResult', '🎉 Custom notification sent successfully!', 'success');
                    log('customResult', '📋 Message ID: ' + data.messageId, 'info');
                    log('customResult', '👀 Check your screen for the notification!', 'info');
                } else {
                    log('customResult', '❌ Failed to send notification: ' + data.error, 'error');
                }

            } catch (error) {
                log('customResult', `❌ Network error: ${error.message}`, 'error');
            }
        }

        // Initialize with some example text
        window.addEventListener('load', () => {
            document.getElementById('customTitle').value = '🎉 PinPal Custom Notification';
            document.getElementById('customBody').value = 'This is a custom notification sent via FCM REST API v1!';
        });
    </script>
</body>

</html>