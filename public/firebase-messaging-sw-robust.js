// Firebase Messaging Service Worker - Versão Robusta
console.log('🔧 Firebase SW: Starting registration...');

// Import Firebase scripts
try {
    importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
    importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');
    console.log('✅ Firebase SW: Scripts imported successfully');
} catch (error) {
    console.error('❌ Firebase SW: Failed to import scripts:', error);
}

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
    authDomain: "iconpal-cf925.firebaseapp.com",
    projectId: "iconpal-cf925",
    storageBucket: "iconpal-cf925.appspot.com",
    messagingSenderId: "887109976546",
    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
};

// Initialize Firebase
try {
    if (typeof firebase !== 'undefined') {
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();
        console.log('✅ Firebase SW: Firebase initialized successfully');
        
        // Background message handler
        messaging.onBackgroundMessage((payload) => {
            console.log('📨 Firebase SW: Background message received:', payload);
            
            const notificationTitle = payload.notification?.title || 'PinPal Notification';
            const notificationOptions = {
                body: payload.notification?.body || 'You have a new notification',
                icon: '/favicon.ico',
                badge: '/favicon.ico'
            };
            
            return self.registration.showNotification(notificationTitle, notificationOptions);
        });
    } else {
        console.error('❌ Firebase SW: Firebase not available');
    }
} catch (error) {
    console.error('❌ Firebase SW: Initialization failed:', error);
}

// Install event
self.addEventListener('install', (event) => {
    console.log('🔧 Firebase SW: Installing...');
    self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
    console.log('✅ Firebase SW: Activated');
    event.waitUntil(self.clients.claim());
});

console.log('✅ Firebase SW: Setup complete'); 