<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Configuration Validation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 700px;
            margin: 20px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        .result {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .warning { color: #ffd43b; }
        .info { color: #74c0fc; }
    </style>
</head>
<body>
    <h1>🔍 Firebase Configuration Validation</h1>
    
    <div>
        <h3>Configuration Details:</h3>
        <p><strong>Sender ID:</strong> 887109976546</p>
        <p><strong>VAPID Key:</strong> BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA</p>
        <p><strong>Project ID:</strong> iconpal-cf925</p>
    </div>
    
    <div>
        <button onclick="validateProjectAccess()">🔐 Validate Project Access</button>
        <button onclick="testInstallationsAPI()">⚙️ Test Installations API</button>
        <button onclick="testWithAlternativeConfig()">🔄 Try Alternative Config</button>
        <button onclick="testVAPIDKey()">🔑 Validate VAPID Key</button>
    </div>
    
    <div id="result" class="result">Click a button to start validation...</div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"></script>

    <script>
        function log(message, type = 'info') {
            const element = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += '<span class="' + className + '">[' + timestamp + '] ' + message + '</span>\n';
            element.scrollTop = element.scrollHeight;
        }

        function clear() {
            document.getElementById('result').innerHTML = '';
        }

        async function validateProjectAccess() {
            clear();
            log('🔐 Validating Firebase project access...', 'info');
            
            try {
                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                log('📋 Testing API key validity...', 'info');
                const testUrl = 'https://firebase.googleapis.com/v1/projects/iconpal-cf925?key=' + firebaseConfig.apiKey;
                
                try {
                    const response = await fetch(testUrl);
                    const data = await response.text();
                    
                    if (response.ok) {
                        log('✅ API key is valid - project accessible', 'success');
                        log('📋 Project data: ' + data.substring(0, 200) + '...', 'info');
                    } else {
                        log('❌ API key validation failed: ' + response.status + ' ' + response.statusText, 'error');
                        log('📋 Response: ' + data, 'error');
                    }
                } catch (fetchError) {
                    log('❌ Network error testing API key: ' + fetchError.message, 'error');
                }

                log('🔥 Testing Firebase app initialization...', 'info');
                const app = firebase.initializeApp(firebaseConfig, 'validation-test-' + Date.now());
                log('✅ Firebase app initialized successfully', 'success');
                
            } catch (error) {
                log('❌ Project validation failed: ' + error.message, 'error');
                console.error('Validation error:', error);
            }
        }

        async function testInstallationsAPI() {
            clear();
            log('⚙️ Testing Firebase Installations API directly...', 'info');
            
            try {
                const firebaseConfig = {
                    apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
                    authDomain: "iconpal-cf925.firebaseapp.com",
                    projectId: "iconpal-cf925",
                    storageBucket: "iconpal-cf925.appspot.com",
                    messagingSenderId: "887109976546",
                    appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
                };

                log('🔧 Creating installation request...', 'info');
                const installationData = {
                    appId: firebaseConfig.appId,
                    sdkVersion: 'w:10.7.1',
                    platform: 'WEB'
                };

                const installationUrl = 'https://firebaseinstallations.googleapis.com/v1/projects/iconpal-cf925/installations';
                
                const response = await fetch(installationUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-goog-api-key': firebaseConfig.apiKey
                    },
                    body: JSON.stringify(installationData)
                });

                const responseData = await response.text();
                
                if (response.ok) {
                    log('✅ Installations API working correctly', 'success');
                    log('📋 Installation response: ' + responseData, 'success');
                } else {
                    log('❌ Installations API error: ' + response.status + ' ' + response.statusText, 'error');
                    log('📋 Error response: ' + responseData, 'error');
                }
                
            } catch (error) {
                log('❌ Installations API test failed: ' + error.message, 'error');
                console.error('Installations API error:', error);
            }
        }

        async function testWithAlternativeConfig() {
            clear();
            log('🔄 Testing with alternative Firebase configuration...', 'info');
            
            try {
                // Test with a known working Firebase demo project
                const demoConfig = {
                    apiKey: "AIzaSyBdVl-cTICSwYKrZ95SuvNw7dbQyM-Rtb0",
                    authDomain: "fir-demo-project.firebaseapp.com",
                    projectId: "fir-demo-project",
                    storageBucket: "fir-demo-project.appspot.com",
                    messagingSenderId: "618104808510",
                    appId: "1:618104808510:web:d1b0a1c3b8c8d5e9f7a2b3"
                };

                log('🔥 Initializing with demo config...', 'info');
                const app = firebase.initializeApp(demoConfig, 'demo-test-' + Date.now());
                log('✅ Demo Firebase app initialized', 'success');
                
                log('📱 Testing messaging with demo config...', 'info');
                const messaging = firebase.messaging(app);
                log('✅ Demo messaging instance created', 'success');
                
                log('🎯 This confirms Firebase SDK is working correctly', 'success');
                log('⚠️ The issue is specific to your project configuration', 'warning');
                
            } catch (error) {
                log('❌ Alternative config test failed: ' + error.message, 'error');
                log('📋 This suggests a broader Firebase SDK issue', 'error');
                console.error('Alternative config error:', error);
            }
        }

        async function testVAPIDKey() {
            clear();
            log('🔑 Validating VAPID key format and structure...', 'info');
            
            const vapidKey = "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";
            
            log('📏 VAPID key length: ' + vapidKey.length + ' characters', 'info');
            
            if (vapidKey.length === 88) {
                log('✅ VAPID key length is correct (88 characters)', 'success');
            } else {
                log('❌ VAPID key length is incorrect (should be 88)', 'error');
            }
            
            if (vapidKey.startsWith('B')) {
                log('✅ VAPID key format is correct (starts with B)', 'success');
            } else {
                log('❌ VAPID key format is incorrect (should start with B)', 'error');
            }
            
            // Test base64 URL decoding
            try {
                const base64urlPattern = /^[A-Za-z0-9_-]+$/;
                if (base64urlPattern.test(vapidKey)) {
                    log('✅ VAPID key uses valid base64url characters', 'success');
                } else {
                    log('❌ VAPID key contains invalid characters', 'error');
                }
            } catch (error) {
                log('❌ VAPID key validation error: ' + error.message, 'error');
            }
            
            log('📋 VAPID key appears to be properly formatted', 'info');
        }
    </script>
</body>
</html>
