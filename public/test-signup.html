<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Signup Simples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }

        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
        }

        input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #555;
            border-radius: 5px;
            background: #333;
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }

        button {
            width: 100%;
            padding: 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background: #45a049;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .success {
            background: #1b4332;
            color: #b7e4c7;
        }

        .error {
            background: #590e0e;
            color: #ffb3ba;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🧪 Teste Signup Direto</h1>

        <input type="email" id="email" placeholder="Email">
        <input type="password" id="password" placeholder="Password" value="TestPass123!">
        <input type="text" id="username" placeholder="Username">
        <input type="text" id="firstName" placeholder="First Name" value="Teste">
        <input type="text" id="lastName" placeholder="Last Name" value="User">

        <button onclick="testSignup()">🚀 Criar Conta</button>

        <div id="result" class="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
        import {
            getAuth,
            createUserWithEmailAndPassword,
            updateProfile
        } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js';

        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
            authDomain: "iconpal-cf925.firebaseapp.com",
            projectId: "iconpal-cf925",
            storageBucket: "iconpal-cf925.appspot.com",
            messagingSenderId: "887109976546",
            appId: "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        window.auth = auth;
        window.createUserWithEmailAndPassword = createUserWithEmailAndPassword;
        window.updateProfile = updateProfile;

        function log(message, type = 'success') {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            console.log(message);
        }

        window.log = log;
        log('Firebase inicializado! Pronto para teste.', 'success');
    </script>

    <script>
        async function testSignup() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const username = document.getElementById('username').value;
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;

            if (!email || !password || !username || !firstName) {
                log('❌ Preencha todos os campos obrigatórios', 'error');
                return;
            }

            try {
                log('🔄 Criando conta no Firebase Auth...', 'success');

                // 1. Criar usuário no Firebase Auth
                const userCredential = await window.createUserWithEmailAndPassword(window.auth, email, password);
                const user = userCredential.user;

                log(`✅ Usuário criado no Firebase!
UID: ${user.uid}
Email: ${user.email}

🔄 Atualizando perfil...`, 'success');

                // 2. Atualizar perfil
                const fullName = `${firstName} ${lastName}`.trim();
                await window.updateProfile(user, { displayName: fullName });

                log(`✅ Perfil atualizado!
Nome: ${fullName}

🔄 Sincronizando com PostgreSQL...`, 'success');

                // 3. Sincronizar com PostgreSQL
                const syncResponse = await fetch('http://localhost:3001/api/users/auth/ensure', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: user.uid,
                        email: user.email,
                        username: username,
                        displayName: fullName
                    })
                });

                if (syncResponse.ok) {
                    const syncData = await syncResponse.json();
                    log(`🎉 SUCESSO COMPLETO!

✅ Firebase Auth: OK
✅ PostgreSQL: OK
✅ Sincronização: OK

Dados do usuário:
- UID: ${user.uid}
- Email: ${user.email}
- Username: ${username}
- Nome: ${fullName}

🔑 Agora você pode fazer login no site principal!
🌐 Acesse: http://localhost:5773`, 'success');
                } else {
                    const errorData = await syncResponse.text();
                    log(`⚠️ Firebase OK, mas erro no PostgreSQL:
${errorData}

✅ Usuário criado no Firebase
❌ Sincronização falhou

Você pode tentar fazer login mesmo assim.`, 'error');
                }

            } catch (error) {
                let errorMessage = `❌ ERRO NO SIGNUP:
Código: ${error.code}
Mensagem: ${error.message}`;

                if (error.code === 'auth/email-already-in-use') {
                    errorMessage += `

💡 SOLUÇÃO: Esse email já existe!
Tente fazer LOGIN em vez de signup.`;
                } else if (error.code === 'auth/weak-password') {
                    errorMessage += `

💡 SOLUÇÃO: Senha muito fraca.
Use pelo menos 6 caracteres.`;
                } else if (error.code === 'auth/invalid-email') {
                    errorMessage += `

💡 SOLUÇÃO: Email inválido.
Verifique o formato do email.`;
                }

                log(errorMessage, 'error');
            }
        }

        // Auto-gerar email e username únicos
        window.addEventListener('load', () => {
            const timestamp = Date.now();
            document.getElementById('email').value = `teste.${timestamp}@example.com`;
            document.getElementById('username').value = `user${timestamp}`;
        });
    </script>
</body>

</html>