VITE_GOOGLE_MAPS_API_KEY=AIzaSyCsK8De7eoNEVV93I2Du0szP5wGKl1rTws
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=pinpal_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=pinpal123
VITE_GOOGLE_MAPS_MAP_ID=DEMO_MAP_ID
# Stripe Configuration for Development
#STRIPE_SECRET_KEY=sk_test_51234567890abcdef_test_key_placeholder
#STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef_test_key_placeholder

# Firebase Configuration
VITE_FIREBASE_API_KEY=AIzaSyAJF8mPMpHyPqGhKqFwL8C7dQJxKzNfQ2M
VITE_FIREBASE_AUTH_DOMAIN=iconpal-cf925.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=iconpal-cf925
VITE_FIREBASE_STORAGE_BUCKET=iconpal-cf925.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=************
VITE_FIREBASE_APP_ID=1:************:web:abcdefghijklmnop

# Firebase Cloud Messaging VAPID Key
# Obtida do Firebase Console → Cloud Messaging → Web Push certificates
# COLE SUA CHAVE VAPID AQUI (substitua a linha abaixo)
VITE_FIREBASE_VAPID_KEY=BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA

# API Configuration
VITE_API_BASE_URL=http://localhost:3001

# Firebase Service Account (for FCM REST API v1)
FIREBASE_SERVICE_ACCOUNT_PATH=./firebase-service-account.json
GOOGLE_APPLICATION_CREDENTIALS=./firebase-service-account.json
VITE_WEBSOCKET_URL=ws://localhost:3001

# Development Mode
NODE_ENV=development
VITE_NODE_ENV=development

# Debug Flags
VITE_DEBUG_MODE=true
VITE_ENABLE_CONSOLE_LOGS=true

# Performance Monitoring
VITE_ENABLE_WEB_VITALS=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
