// FCM REST API v1 Routes
// Implementação seguindo: https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

import express from 'express';
const router = express.Router();

// Import FCM v1 Service
import FCMv1Service from '../../../server/fcm-v1-service.js';
const fcmService = new FCMv1Service();

/**
 * POST /api/notifications/fcm/send
 * Enviar notificação usando FCM REST API v1
 */
router.post('/send', async (req, res) => {
  try {
    const { token, notification, data, webpush } = req.body;
    
    // Validação dos dados obrigatórios
    if (!token) {
      return res.status(400).json({ 
        success: false,
        error: 'FCM token is required',
        code: 'MISSING_TOKEN'
      });
    }
    
    if (!notification || !notification.title || !notification.body) {
      return res.status(400).json({ 
        success: false,
        error: 'Notification title and body are required',
        code: 'MISSING_NOTIFICATION_DATA'
      });
    }

    console.log('📱 Sending FCM notification via API v1...');
    console.log('🎯 Target token:', token.substring(0, 20) + '...');
    console.log('📋 Notification:', notification);
    
    const result = await fcmService.sendNotification(token, notification, data, webpush);
    
    if (result.success) {
      console.log('✅ Notification sent successfully');
      res.json({
        success: true,
        messageId: result.messageId,
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('❌ Failed to send notification:', result.error);
      res.status(500).json({
        success: false,
        error: result.error,
        code: 'SEND_FAILED'
      });
    }
    
  } catch (error) {
    console.error('❌ Error in FCM send route:', error);
    res.status(500).json({ 
      success: false,
      error: 'Internal server error',
      details: error.message,
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * POST /api/notifications/fcm/send-test
 * Enviar notificação de teste
 */
router.post('/send-test', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ 
        success: false,
        error: 'FCM token is required',
        code: 'MISSING_TOKEN'
      });
    }

    console.log('🧪 Sending test notification...');
    console.log('🎯 Target token:', token.substring(0, 20) + '...');
    
    const result = await fcmService.sendTestNotification(token);
    
    if (result.success) {
      console.log('✅ Test notification sent successfully');
      res.json({
        success: true,
        messageId: result.messageId,
        message: 'Test notification sent successfully',
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('❌ Failed to send test notification:', result.error);
      res.status(500).json({
        success: false,
        error: result.error,
        code: 'TEST_SEND_FAILED'
      });
    }
    
  } catch (error) {
    console.error('❌ Error in test notification route:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to send test notification',
      details: error.message,
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * POST /api/notifications/fcm/validate-token
 * Validar se um token FCM é válido
 */
router.post('/validate-token', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ 
        success: false,
        error: 'FCM token is required',
        code: 'MISSING_TOKEN'
      });
    }

    console.log('🔍 Validating FCM token...');
    console.log('🎯 Token:', token.substring(0, 20) + '...');
    
    const isValid = await fcmService.validateToken(token);
    
    console.log(`📋 Token validation result: ${isValid ? 'VALID' : 'INVALID'}`);
    
    res.json({ 
      success: true,
      valid: isValid,
      token: token.substring(0, 20) + '...',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error in token validation route:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to validate token',
      details: error.message,
      code: 'VALIDATION_ERROR'
    });
  }
});

/**
 * POST /api/notifications/fcm/send-multicast
 * Enviar notificação para múltiplos tokens
 */
router.post('/send-multicast', async (req, res) => {
  try {
    const { tokens, notification, data } = req.body;
    
    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return res.status(400).json({ 
        success: false,
        error: 'Array of FCM tokens is required',
        code: 'MISSING_TOKENS'
      });
    }
    
    if (!notification || !notification.title || !notification.body) {
      return res.status(400).json({ 
        success: false,
        error: 'Notification title and body are required',
        code: 'MISSING_NOTIFICATION_DATA'
      });
    }

    console.log(`📱 Sending multicast notification to ${tokens.length} tokens...`);
    
    const results = await fcmService.sendMulticastNotification(tokens, notification, data);
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    console.log(`📊 Multicast results: ${successCount} success, ${failureCount} failures`);
    
    res.json({
      success: true,
      totalCount: results.length,
      successCount,
      failureCount,
      results,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error in multicast route:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to send multicast notification',
      details: error.message,
      code: 'MULTICAST_ERROR'
    });
  }
});

/**
 * GET /api/notifications/fcm/status
 * Verificar status do serviço FCM
 */
router.get('/status', async (req, res) => {
  try {
    const status = fcmService.getStatus();
    res.json({
      success: true,
      ...status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error getting FCM status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ✅ NOVO: Endpoint para fornecer VAPID key
router.get('/vapid-key', (req, res) => {
  try {
    // VAPID key do Firebase (pública, pode ser exposta)
    const vapidKey = process.env.VITE_FIREBASE_VAPID_KEY || "BPWYMHZhOddtoVrhtO2Qk01aQORhXmHd3L5ndeL4_I17g1Il1o81dG2vnFvX4uq16P2ecExgtLr3f8OyOI6ScWA";
    
    res.json({
      success: true,
      vapidKey: vapidKey,
      note: 'VAPID key is public and safe to expose'
    });
  } catch (error) {
    console.error('❌ Error getting VAPID key:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ✅ NOVO: Endpoint para fornecer configuração Firebase
router.get('/config', (req, res) => {
  try {
    // Firebase configuration - now using corrected environment variables
    const firebaseConfig = {
      apiKey: process.env.VITE_FIREBASE_API_KEY || "AIzaSyDYS33rFAoJGhQ68VyqljLKoZAE2GKZoZc",
      authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN || "iconpal-cf925.firebaseapp.com",
      projectId: process.env.VITE_FIREBASE_PROJECT_ID || "iconpal-cf925",
      storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET || "iconpal-cf925.appspot.com",
      messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "887109976546",
      appId: process.env.VITE_FIREBASE_APP_ID || "1:887109976546:web:a8b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
    };
    
    res.json({
      success: true,
      config: firebaseConfig,
      note: 'Firebase configuration for FCM testing (using environment variables with fallbacks)'
    });
  } catch (error) {
    console.error('❌ Error getting Firebase config:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
