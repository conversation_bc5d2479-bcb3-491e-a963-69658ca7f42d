import express from 'express';
import { getPool } from '../../../config/database.js';
import stripeService from '../../../services/stripeService.js';

const router = express.Router();

/**
 * GET /api/marketplace/sellers/connect/status/:userId
 * Verificar status da conta Stripe Connect do vendedor
 */
router.get('/connect/status/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    console.log('🔍 Checking Stripe Connect status for user:', userId);

    const pool = await getPool();
    const userResult = await pool.query(`
      SELECT 
        stripe_account_id, 
        stripe_onboarding_complete, 
        stripe_payouts_enabled,
        email,
        first_name,
        last_name,
        display_name,
        seller_enabled
      FROM "user" 
      WHERE id = $1
    `, [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const user = userResult.rows[0];
    const hasStripeAccount = !!user.stripe_account_id;
    const onboardingComplete = user.stripe_onboarding_complete || false;
    const payoutsEnabled = user.stripe_payouts_enabled || false;
    const sellerEnabled = user.seller_enabled || false;

    console.log('📊 User Stripe status:', {
      hasAccount: hasStripeAccount,
      onboardingComplete,
      payoutsEnabled,
      sellerEnabled
    });

    console.log('🔍 Status check flow:');
    console.log('  - sellerEnabled:', sellerEnabled);
    console.log('  - hasStripeAccount:', hasStripeAccount);
    console.log('  - onboardingComplete:', onboardingComplete);

    // MODELO FACEBOOK MARKETPLACE:
    // 1. ETAPA BÁSICA (Anunciar pins): Apenas ativar seller_enabled = true
    // 2. ETAPA COMPLETA (Receber pagamentos): Stripe Connect onboarding completo

    // Se não tem seller_enabled, precisa ativar funcionalidade básica
    if (!sellerEnabled) {
      return res.json({
        success: true,
        hasStripeAccount: false,
        accountStatus: 'not_activated',
        payoutsEnabled: false,
        canSell: false, // Não pode nem anunciar ainda
        canList: false, // Não pode listar pins
        needsBasicActivation: true, // Precisa ativar funcionalidade básica
        needsPaymentSetup: false, // Ainda não precisa do Stripe
        onboardingUrl: null,
        message: 'Seller features not activated. Enable to start listing pins.'
      });
    }

    // Se tem seller_enabled mas não tem Stripe, pode anunciar mas não receber pagamentos
    if (sellerEnabled && !hasStripeAccount) {
      return res.json({
        success: true,
        hasStripeAccount: false,
        accountStatus: 'basic_active',
        payoutsEnabled: false,
        canSell: false, // Não pode receber pagamentos ainda
        canList: true, // PODE listar pins (modelo Facebook)
        needsBasicActivation: false,
        needsPaymentSetup: true, // Precisa configurar pagamentos para vender
        onboardingUrl: null,
        message: 'You can list pins. Set up payments to start selling.'
      });
    }

    // Se tem Stripe mas não está completo, pode anunciar mas não pode sacar
    if (hasStripeAccount && !onboardingComplete) {
      try {
        const onboardingData = await stripeService.createOnboardingLink(user.stripe_account_id);
        
        return res.json({
          success: true,
          hasStripeAccount: true,
          accountStatus: 'incomplete',
          payoutsEnabled: false,
          canSell: true, // PODE receber pagamentos (ficam "em espera")
          canList: true, // PODE listar pins
          needsBasicActivation: false,
          needsPaymentSetup: false,
          canWithdraw: false, // NÃO pode sacar ainda
          onboardingUrl: onboardingData.url,
          message: 'You can sell pins. Complete setup to withdraw earnings.'
        });
      } catch (error) {
        console.error('Error creating onboarding URL:', error);
        return res.json({
          success: true,
          hasStripeAccount: true,
          accountStatus: 'incomplete',
          payoutsEnabled: false,
          canSell: false,
          canList: true,
          needsBasicActivation: false,
          needsPaymentSetup: true,
          onboardingUrl: null,
          message: 'Payment setup incomplete. Complete to start selling.'
        });
      }
    }

    // Se está tudo completo, pode fazer tudo
    if (hasStripeAccount && onboardingComplete && payoutsEnabled) {
      return res.json({
        success: true,
        hasStripeAccount: true,
        accountStatus: 'complete',
        payoutsEnabled: true,
        canSell: true, // PODE receber pagamentos
        canList: true, // PODE listar pins
        canWithdraw: true, // PODE sacar dinheiro
        needsBasicActivation: false,
        needsPaymentSetup: false,
        onboardingUrl: null,
        message: 'Seller account fully active. You can list, sell, and withdraw.'
      });
    }

    // Fallback - conta existe mas há algum problema
    return res.json({
      success: true,
      hasStripeAccount,
      accountStatus: 'unknown',
      payoutsEnabled,
      canSell: false,
      canList: sellerEnabled,
      needsBasicActivation: !sellerEnabled,
      needsPaymentSetup: !hasStripeAccount,
      onboardingUrl: null,
      message: 'Account status unclear. Please contact support.'
    });

  } catch (error) {
    console.error('Error checking seller status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/marketplace/sellers/connect/create-account
 * Criar conta Stripe Connect para vendedor
 */
router.post('/connect/create-account', async (req, res) => {
  try {
    const { userId, email, userInfo } = req.body;

    if (!userId || !email) {
      return res.status(400).json({
        success: false,
        error: 'User ID and email are required'
      });
    }

    console.log('🔄 Creating Stripe Connect account for user:', userId);

    // Verificar se usuário já tem conta Connect
    const pool = await getPool();
    const existingUser = await pool.query(`
      SELECT stripe_account_id, stripe_onboarding_complete 
      FROM "user" 
      WHERE id = $1
    `, [userId]);

    if (existingUser.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const user = existingUser.rows[0];
    
    // Se já tem conta, retornar status atual
    if (user.stripe_account_id) {
      return res.json({
        success: true,
        accountId: user.stripe_account_id,
        onboardingComplete: user.stripe_onboarding_complete,
        message: 'Stripe Connect account already exists'
      });
    }

    // Criar nova conta Connect
    const accountData = await stripeService.createConnectAccount({
      userId,
      email,
      firstName: userInfo?.firstName,
      lastName: userInfo?.lastName,
      displayName: userInfo?.displayName
    });

    // Salvar account ID no banco
    await pool.query(`
      UPDATE "user" 
      SET 
        stripe_account_id = $1,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [accountData.accountId, userId]);

    console.log('✅ Stripe Connect account created:', accountData.accountId);

    res.json({
      success: true,
      accountId: accountData.accountId,
      onboardingUrl: accountData.onboardingUrl,
      message: 'Stripe Connect account created successfully'
    });

  } catch (error) {
    console.error('Error creating Stripe Connect account:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/marketplace/sellers/connect/webhook
 * Webhook para atualizar status do onboarding
 */
router.post('/connect/webhook', async (req, res) => {
  try {
    const { accountId, onboardingComplete, payoutsEnabled } = req.body;

    console.log('🔔 Stripe Connect webhook received:', {
      accountId,
      onboardingComplete,
      payoutsEnabled
    });

    const pool = await getPool();
    
    // Atualizar status no banco
    const result = await pool.query(`
      UPDATE "user" 
      SET 
        stripe_onboarding_complete = $1,
        stripe_payouts_enabled = $2,
        updated_at = CURRENT_TIMESTAMP
      WHERE stripe_account_id = $3
      RETURNING id, email, display_name
    `, [onboardingComplete, payoutsEnabled, accountId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User with this Stripe account not found'
      });
    }

    const user = result.rows[0];
    console.log('✅ Updated Stripe status for user:', user.id);

    res.json({
      success: true,
      userId: user.id,
      message: 'Stripe Connect status updated successfully'
    });

  } catch (error) {
    console.error('Error processing Stripe Connect webhook:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/marketplace/sellers/dashboard/:userId
 * Dashboard do vendedor com estatísticas
 */
router.get('/dashboard/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    console.log('📊 Getting seller dashboard for user:', userId);

    const pool = await getPool();

    // Verificar se é vendedor ativo
    const userResult = await pool.query(`
      SELECT 
        stripe_account_id, 
        stripe_onboarding_complete, 
        stripe_payouts_enabled
      FROM "user" 
      WHERE id = $1
    `, [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const user = userResult.rows[0];

    if (!user.stripe_account_id) {
      return res.json({
        success: true,
        totalEarnings: 0,
        pendingPayouts: 0,
        completedSales: 0,
        activeListings: 0,
        stripeAccountStatus: 'not_connected',
        message: 'User needs to set up Stripe Connect first'
      });
    }

    // Buscar estatísticas de vendas
    const salesResult = await pool.query(`
      SELECT 
        COUNT(*) as total_sales,
        COALESCE(SUM(amount), 0) as total_earnings,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as completed_sales,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_sales
      FROM orders o
      JOIN pin p ON o.pin_id = p.id
      WHERE p.user_id = $1
    `, [userId]);

    // Buscar pins ativos para venda
    const listingsResult = await pool.query(`
      SELECT COUNT(*) as active_listings
      FROM pin
      WHERE user_id = $1 AND availability = 'sale'
    `, [userId]);

    const sales = salesResult.rows[0];
    const listings = listingsResult.rows[0];

    // Calcular earnings em dólares (converter de centavos)
    const totalEarnings = parseFloat(sales.total_earnings) / 100;

    res.json({
      success: true,
      totalEarnings: totalEarnings,
      pendingPayouts: 0, // TODO: Implementar via Stripe API
      completedSales: parseInt(sales.completed_sales),
      activeListings: parseInt(listings.active_listings),
      stripeAccountStatus: user.stripe_onboarding_complete ? 'complete' : 'incomplete',
      payoutsEnabled: user.stripe_payouts_enabled || false
    });

  } catch (error) {
    console.error('Error getting seller dashboard:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/marketplace/sellers/connect/create-account-internal
 * Criar conta Stripe Connect usando dados coletados pelo PinPal (sem redirect)
 */
router.post('/connect/create-account-internal', async (req, res) => {
  try {
    const { userId, sellerInfo } = req.body;
    
    if (!userId || !sellerInfo) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId and sellerInfo'
      });
    }

    console.log('🏪 Creating internal Stripe Connect account for user:', userId);
    console.log('📋 Seller info received:', {
      name: `${sellerInfo.firstName} ${sellerInfo.lastName}`,
      email: sellerInfo.email,
      hasAddress: !!sellerInfo.address?.line1,
      hasBankAccount: !!sellerInfo.bankAccount?.routingNumber
    });

    // Validar dados obrigatórios
    const requiredFields = [
      'firstName', 'lastName', 'email', 'phone', 'dateOfBirth'
    ];
    
    for (const field of requiredFields) {
      if (!sellerInfo[field]) {
        return res.status(400).json({
          success: false,
          error: `Missing required field: ${field}`
        });
      }
    }

    // Validar endereço
    if (!sellerInfo.address?.line1 || !sellerInfo.address?.city || 
        !sellerInfo.address?.state || !sellerInfo.address?.postalCode) {
      return res.status(400).json({
        success: false,
        error: 'Complete address information is required'
      });
    }

    // Validar informações bancárias
    if (!sellerInfo.bankAccount?.routingNumber || !sellerInfo.bankAccount?.accountNumber ||
        !sellerInfo.bankAccount?.accountHolderName) {
      return res.status(400).json({
        success: false,
        error: 'Complete bank account information is required'
      });
    }

    // Criar conta Stripe Connect com informações completas
    const stripeResult = await stripeService.createConnectAccountInternal({
      userId,
      personalInfo: {
        firstName: sellerInfo.firstName,
        lastName: sellerInfo.lastName,
        email: sellerInfo.email,
        phone: sellerInfo.phone,
        dateOfBirth: sellerInfo.dateOfBirth
      },
      address: sellerInfo.address,
      bankAccount: sellerInfo.bankAccount,
      business: sellerInfo.business || { type: 'individual' }
    });

    if (!stripeResult.success) {
      return res.status(500).json({
        success: false,
        error: stripeResult.error || 'Failed to create Stripe Connect account'
      });
    }

    // Salvar informações no banco de dados
    const pool = await getPool();
    
    await pool.query(`
      UPDATE "user" SET 
        stripe_account_id = $1,
        stripe_onboarding_completed = $2,
        stripe_charges_enabled = $3,
        stripe_payouts_enabled = $4,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
    `, [
      stripeResult.accountId,
      stripeResult.onboardingComplete || false,
      stripeResult.chargesEnabled || false,
      stripeResult.payoutsEnabled || false,
      userId
    ]);

    console.log('✅ Stripe Connect account created and saved:', stripeResult.accountId);

    res.json({
      success: true,
      message: 'Seller account created successfully',
      data: {
        accountId: stripeResult.accountId,
        status: stripeResult.onboardingComplete ? 'active' : 'pending_verification',
        canSell: stripeResult.chargesEnabled && stripeResult.payoutsEnabled,
        estimatedActivation: stripeResult.onboardingComplete ? 'immediate' : '1-2 business days'
      }
    });

  } catch (error) {
    console.error('Error creating internal Stripe Connect account:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

/**
 * POST /api/marketplace/sellers/connect/refresh-onboarding/:userId
 * Recriar link de onboarding para conta Stripe existente
 */
router.post('/connect/refresh-onboarding/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    console.log('🔄 Refreshing Stripe onboarding for user:', userId);

    const pool = await getPool();
    const userResult = await pool.query(`
      SELECT 
        stripe_account_id, 
        email,
        first_name,
        last_name,
        display_name
      FROM "user" 
      WHERE id = $1
    `, [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const user = userResult.rows[0];
    
    if (!user.stripe_account_id) {
      return res.status(400).json({
        success: false,
        error: 'User does not have a Stripe account'
      });
    }

    // Recriar link de onboarding usando o account ID existente
    const onboardingData = await stripeService.createOnboardingLink(user.stripe_account_id);

    console.log('✅ Refreshed onboarding URL:', onboardingData.url);

    res.json({
      success: true,
      onboardingUrl: onboardingData.url,
      accountId: user.stripe_account_id,
      message: 'Onboarding link refreshed successfully'
    });

  } catch (error) {
    console.error('Error refreshing onboarding link:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/marketplace/sellers/activate-basic
 * ETAPA 1: Ativar funcionalidade básica de vendedor (modelo Facebook Marketplace)
 * Permite listar pins sem configurar pagamentos ainda
 */
router.post('/activate-basic', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    console.log('🔄 Activating basic seller features for user:', userId);

    const pool = await getPool();
    
    // Verificar se usuário existe
    const userCheck = await pool.query(`
      SELECT id, seller_enabled, email, first_name, last_name 
      FROM "user" 
      WHERE id = $1
    `, [userId]);

    if (userCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const user = userCheck.rows[0];

    // Se já está ativado, retornar sucesso
    if (user.seller_enabled) {
      return res.json({
        success: true,
        message: 'Seller features already activated',
        status: 'basic_active',
        canList: true,
        needsPaymentSetup: true
      });
    }

    // Ativar funcionalidade básica de vendedor
    await pool.query(`
      UPDATE "user" 
      SET 
        seller_enabled = true,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [userId]);

    console.log('✅ Basic seller features activated for user:', userId);

    res.json({
      success: true,
      message: 'Seller features activated successfully! You can now list pins.',
      status: 'basic_active',
      canList: true,
      canSell: false, // Ainda não pode receber pagamentos
      needsPaymentSetup: true,
      nextStep: 'Set up payments to start selling and receiving money'
    });

  } catch (error) {
    console.error('Error activating basic seller features:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Enable capabilities for seller account
router.post('/:sellerId/stripe/enable-capabilities', async (req, res) => {
  const { sellerId } = req.params;
  
  console.log('🔧 API: Enabling Stripe capabilities for seller:', sellerId);
  
  try {
    // Get seller's Stripe account ID
    const pool = await getPool();
    const sellerQuery = 'SELECT stripe_account_id FROM "user" WHERE id = $1';
    const sellerResult = await pool.query(sellerQuery, [sellerId]);
    
    if (sellerResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Seller not found'
      });
    }
    
    const stripeAccountId = sellerResult.rows[0].stripe_account_id;
    
    if (!stripeAccountId) {
      return res.status(400).json({
        success: false,
        error: 'Seller does not have a Stripe Connect account'
      });
    }
    
    // Enable capabilities using StripeService
    const result = await stripeService.enableAccountCapabilities(stripeAccountId);
    
    console.log('✅ Capabilities enabled result:', result);
    
    res.json({
      success: true,
      accountId: stripeAccountId,
      capabilities: result.capabilities,
      activeCapabilities: result.activeCapabilities,
      pendingCapabilities: result.pendingCapabilities,
      message: result.message
    });
    
  } catch (error) {
    console.error('Error enabling capabilities:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Complete account setup for testing
router.post('/:sellerId/stripe/complete-for-testing', async (req, res) => {
  const { sellerId } = req.params;
  
  console.log('🧪 API: Completing Stripe account for testing:', sellerId);
  
  try {
    // Get seller's Stripe account ID
    const pool = await getPool();
    const sellerQuery = 'SELECT stripe_account_id FROM "user" WHERE id = $1';
    const sellerResult = await pool.query(sellerQuery, [sellerId]);
    
    if (sellerResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Seller not found'
      });
    }
    
    const stripeAccountId = sellerResult.rows[0].stripe_account_id;
    
    if (!stripeAccountId) {
      return res.status(400).json({
        success: false,
        error: 'Seller does not have a Stripe Connect account'
      });
    }
    
    // Complete account for testing using StripeService
    const result = await stripeService.completeAccountForTesting(stripeAccountId);
    
    console.log('✅ Account completion result:', result);
    
    res.json({
      success: true,
      accountId: stripeAccountId,
      chargesEnabled: result.chargesEnabled,
      payoutsEnabled: result.payoutsEnabled,
      capabilities: result.capabilities,
      requirements: result.requirements,
      message: result.message
    });
    
  } catch (error) {
    console.error('Error completing account for testing:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get seller Stripe account status
router.get('/:sellerId/stripe/status', async (req, res) => {
  const { sellerId } = req.params;
  
  console.log('🔍 API: Getting Stripe status for seller:', sellerId);
  
  try {
    // Get seller's Stripe account ID
    const pool = await getPool();
    const sellerQuery = 'SELECT stripe_account_id FROM "user" WHERE id = $1';
    const sellerResult = await pool.query(sellerQuery, [sellerId]);
    
    if (sellerResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Seller not found'
      });
    }
    
    const stripeAccountId = sellerResult.rows[0].stripe_account_id;
    
    if (!stripeAccountId) {
      return res.status(200).json({
        success: true,
        hasStripeAccount: false,
        message: 'Seller does not have a Stripe Connect account'
      });
    }
    
    // Get account status using StripeService
    const status = await stripeService.getAccountStatus(stripeAccountId);
    
    res.json({
      success: true,
      hasStripeAccount: true,
      accountId: stripeAccountId,
      chargesEnabled: status.chargesEnabled,
      payoutsEnabled: status.payoutsEnabled,
      capabilities: status.capabilities,
      requirements: status.requirements,
      accountType: status.accountType
    });
    
  } catch (error) {
    console.error('Error getting account status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router; 